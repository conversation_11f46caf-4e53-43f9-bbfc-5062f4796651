﻿My Trade Methodology - Read Carefully
Below are the fundamentals of my methodology in 15 points, including my overall trading philosophy, my system for risk and trade management, the actionable entry setups I use, as well as some tools for interpreting price action around critical levels. This is a dynamic page that will be constantly expanding.
1) This newsletter is intended to be an ongoing education. In other words, every day I not only discuss my actionable trade plan and entry triggers, but also the setups I use and examples of them in real time (failed breakdown setups, back-tests etc) how I manage trades, and my general philosophy on trading. I illustrate these using real-time examples. Over weeks of reading, the concepts become clearer and more actionable. Trading is a lifelong learning process, and takes significant time investment in the order of thousands of hours - there is *no shortcut* to putting the time in. You are competing against the smartest people in the world, as well as sophisticated algos built to prey on repetitive tendencies of retail traders, and you will not beat them without putting in the leg work and doing things *very different* from the majority who are destined to lose.
The Psychological Foundation
2) I trade seldomly, and in pre-defined windows. Overtrading is the #1 reason most retail traders never achieve profitability and all professional trading is characterized by *picking your spots*, waiting for price to come to you, then being on the sidelines the rest of the time. I shoot for 1 to 3 trades maximum every day. As <PERSON> famously said markets (and therefore most trading days) are 20% trend moves, and 80% chop, and the trend moves are typically before 11am and after 2pm, with the period in between being choppy and poor trading conditions. My trading window is therefore 8am to 11am, and rarely after 2pm. There are exceptions when volatility is higher, so this is a guideline and not a hard rule. An ideal day for me is getting 1 trade in before 11am. If the first trade is a loss, I will try a second but never more. If the first trade is a win, I will wrap it up until the post-2pm session. If I have a good realized gain, there is no reason to risk getting chopped up or taking a loss to grab a second. My general rule of thumb is I try to spend 10% of the day in an actual full trade (around 30mins maximum trading per day) and the rest is holding a runner passively, preparing charts, working on the trade plan, and waiting on quality. Trading is *NOT* a paid by the hour job, the opposite. The less you trade, the more you make.
The average trading day is typically comprised of 1-3 decent level to level moves, and tons of unactionable, trappy chop. Trying to force trades when there are none is the cause of the vast majority of losses for new traders. The market is not a casino, and if your sitting there all day flipping back and forth long and short all day trying to “get rich quick”, you are doing the same thing the 90% of retail traders who lose do.
Plan your trades the night before, wait for them, take only the pre-planned trades, then shut the platform down for the day (and/or hold risk free runners)
3) Small gains add up and compound over time, and trading is a long-term process, not a get rich quick scheme. My goal is not to catch every move, nor is it to catch all of any one move. This is impossible, rarely happens, and it is very costly to try. The goal is also not to “get rich” in one trade - it is not a lottery. Wealth is built over time in markets by running a consistent process, stacking consistent, conservative daily gains, and harnessing the power of compounding. FOMO and greed are the primary barriers to realizing this objective for most new traders, and it is the discontent with a sustainable, realized 10-15 point gain for hopes of the extraordinarily rare 75 point “jackpot” that prevents sustainable account growth. I just want a good level-to-level piece or two a day, while adhering to a process that allows me to profit over a large sample of diverse conditions, and this is the path to true and transformative wealth creation. If price moves 100 points in a day and I catch 20, I am very happy. Think in terms of “base hits”, not “home runs”. Do the math on 10-15 points a day (on average) over the course of a year and one can see it adds up to more than any home run ever will, for those patient enough to take it.
4) What I call “impulse trades” are the primary barrier to profitability all new traders must control to see any sustained P&L growth, and my entire system and philosophy is built around not doing impulse trades. Fear of missing of move, greed, “avenging a loss”, or boredom are not reasons to trade. My trades are all planned the night before. I know the zones I want to engage. I know the action around the zones I am looking to trigger myself to engage. Once I get my gain, I then protect it by not trading more. A huge chunk of unprofitable traders have all the skills and screen time to be profitable, but *just cant sit on their hands*. All the mediocre FOMO & boredom based trades destroy the gains from the A+ setups. Profitability is found on the sidelines in the trades you don't take
5) Most price action each day is unsuitable for trading and therefore being able to watch from the sidelines is a defining skill of successful traders. There is no edge capable of catching all the price action in a given day and trying to force action where there is none is the cause of most losses. You should trade only because one of your preplanned setups triggers in a preplanned zone, when you have clear, high conviction, then you should be watching the rest of the time. On most days, if the above principles are applied, you may may catch a good piece or two of the daily action. One my mentors early on told me if he catches 20% of the total daily action, it is an A+ day. You need to be comfortable with missing moves and being on the sidelines, and with the reality that there is no way to catch them all - if you try, you lose.
It is being sidelined that most beginners struggle with. With some practice, hard work, and a few thousands hours behind the screens, most people can learn to 1) Pre-plan a high quality trade in a high conviction zone to take the next day and 2) Wait on it, and take it. What very few can do, however, is sit on their hands after. The primary difference between successful and unsuccessful traders - and I truly believe this - is that the successful traders have conquered FOMO. Almost anyone can learn to take a good trade. Very, very few, however, can sit on their hands and control FOMO. They end up chasing price, over-trading, and - terrified by “missing a move” - end up taking a slew of mediocre, 50/50 trades that destroy profits, create losses, then create a cycle of overtrading it make it back. You don’t need to trade all day, and you will almost certainly make less if you. Take your piece out, move on.
Trade Management - The Most Important Part
6) My trades are always level to level. This is the core of my edge
This is the entire core of my system and what drives my edge. What really drives P&L is not the entry setup, but the trade management system (what happens *after* entry) and level to level trade management is the core of this. While new traders are obsessed with entries, they won’t make you profitable. You can have the best entries in the world, but if your trade management is not systematized, those entries will result in 1) You’ll exit way too early 2) You’ll exit way too late or 3) You won’t exit, and you’ll go green to red.
Level to level trade management means get in at one level (usually a pre-planned area that you waited on), then always take majority profits first resistance, leave a risk free runner, then reset bias as if seeing the market for the first time. Do not get attached to any one “forecast” as price will spoil all of them. Nobody has a crystal ball, and nobody can predict where price will go. In the below paragraphs, I detail exactly how this is done in practice.
As a day trader, where ES will be in a week, a month, or a year, and how it gets there has 0 bearing on your P&L. Nobody can predict those things anyway, and even if someone gets a broader “target” right, price will likely take such a convoluted, stop-hunting path there, that you won’t be on board anyway. What gets you paid is catching the next level to level move. Let the price path then unfold as it wants.
Why do I trade level to level? This is because of what I call “market math”: Price only trends ~10% of the time on average, with the other 90% being price trading inside ranges. In other words, price on average does not move in straight lines. This means that mathematically - if price does not move in straight lines 90% of the time - then if you are not locking in gains systematically at each level, 90% of the time, your trades (the winning ones, that is) will go “green to red”. You’ll have a nice 7 or 8, or 12 point gain, and it will evaporate. Over time, those evaporated gains are life changing and if you are looking for those “10% of the time” home runs on every trade, you will not only not catch the home runs, but by the time they do happen, your capital will be massively depleted (and you will probably miss it anyway). The “fix” is level to level trading.
A golden rule of price action is that it will nearly always take the most complex, trap-filled, difficult pathway from point A to point B, so that even if your “target” turns out to be right (big if), you very likely won’t make money. When you start trading everyday, you are competing against an army of institutional algorithms, largely coded by teams of PhDs in computer science or other STEM disciplines, utilizing cutting edge machine learning, with deeply verified edges that win much more than they lose. When you place a trade, this is what is on the other side. It stands to reason that someone has to do the losing. Typically, it is (among others) always retail because they are falling into this institutional web of traps over and over due to the fact they are overwhelmingly trying to “predict” what price will do, instead of just taking things one level at a time.
Most days will for example will look something like this (if I had to give an “average” ES Session): Open at 4520, rally to 4540, sell back to 4520, fake below to trap shorts, rally to 4536, take a hard leg down to 4500, then set a low for a day or two. You won’t profit not trading level to level.
Any trading strategy intraday that looks for home runs on every trade and does not take profits on a level-to-level basis, will statistically lose over a large sample since most trades are not multi-level runs.
My basic trade management strategy is therefore summarized as follows: Firstly, do not “guess” where moves will end nor assume that any entry will result in a large move, most don’t. You don’t know, I don’t know, nobody knows which setup will result in a one level move and fail, which will go 4 levels then reverse, or which one will break and rally 100 points. The vast majority of moves only go 1 -3 levels. A smaller subset go 3 or 4, a rare subset go 4+. Therefore: Always lock in majority (75%) profits at first level, leave a 25% runner to go in case we trend. With regards to stop movement, I am fairly discretionary with what I do with my stop here, but my golden rule is I will never let my runner take my entire trade trade. In other words if I have 10 points of profit, I will not let that 25% runner eat that 10 points off profit (this means I could theoretically put my stop on my runner 30 points below my break-even, but in practice I rarely go this deep and it will be somewhere around my break-even). From there, lock in more at second level, leave a fully risk free runner and trail up a stop (usually about 10% of the original trade for me for the final runner).
Each runner can be seen as a little “lottery ticket” that costs nothing, and every 15 or 20 trades, one will reward you with a massive payout. In the section below, I go into detail how I trail my stops. Do this on every single trade, regrdless of what you think, regardless of how you feel, regardless of what you read on the internet and you will not “keep” far more profits than ever before, but you are also far more likely to catch those “home run moves” via the runners when they do occur.
7) How do I set stops/trail stops?
With regard to stop setting and trailing stops: My stop setting is based on two principles. 1) I do not use tight stops. Every trade needs ample space and time to work 2) I do not believe in arbitrary stops. Stops must make sense technically. If I enter at 4500, and there is a support at 4495, I will not put a stop at 4495 - it will go at 4490.
I want to give a trade every chance to work and will never stop out arbitrarily without a support being lost, nor will I stop out due to my interpretation of price action once I am in the trade. Once I enter, I am fully committed to that trade until a stop or target hits. Every trade gets ample space and *all* the time it needs to work. 15 points is my maximum risk for any given trade. In other words, the absolute maximum sized loss I will ever take on a full size trade is 15 points. This is a hard rule, and there is no exceptions, under any circumstance. In practice, my stops usually do come under this value though, often in something like 7-11 point range.
If my 1st target away is 10 points, a 7-11 point stop works out to an initial R/R of ~1 and this perfectly fine, and runners will often ramp the R/R further. If you have an R/R of 1 and a 60-65% win rate, you have a very nicely profitable system. As R/R increases, win rate does drop.
As readers know, I do tighten this as a trade goes on based on technical factors (for example, if some obvious low forms on a 15 minute chart, I will drag it up below there), but if I am 10-15 points green I will never take a full stop out, and if I am close to target and it fails, I will never take a loss on a trade (no green to red) and will try to get out several points above break-even. I won’t put 10 points of profit at risk because price comes 1 point from a target.
It is important to realize wider stops do not mean large risk; risk is a function of position size not of stop size. I manage risk via smaller position size, and a 5 point stop and 15 point stop can have the exact same amount of $ risk if the 15 point stop trade is 3x smaller, etc. Information on how I trail my stops can be found in this newsletter.
8) The core difference between amateur and professional traders is amateur trading is largely about predicting, professional trading is about reacting. My trading, and all professional trading is about reacting to price action - not predicting it. Nobody knows where price is headed, nor can anyone predict the precise pathway price will track intraday. Professional and profitable trading is about knowing the levels, knowing the entry setups, having a disciplined system for managing trades, and being prepared to react in real time to whatever price does - up, down, or sideways. A good trader always has a lean or “base case” but is prepared to react to whatever scenario presents, allowing for profitability regardless what pathway price chooses. One could start the day with a high level of bullish confidence, but if a critical support fails and bear case triggers, it means its time to get bearish and short. Your lean/bias, should never prevent a profitable session. Level to level, one move at a time, reset bias, and let price choose whatever path it wants.
My Core Setups
9) My core pattern is the failed breakdown/breakout, and this comprises over 70% of my entries. Very important information on failed break down strategy found in this newsletter. Below are real time examples of the pattern from prior newsletters.
  

The failed breakout/breakdown setup is premised on the fact that the vast majority of breakouts and breakdowns are traps. Price absolutely loves to break down a support, trap those chasing, then reverse and squeeze the other way. Breakouts are therefore exceptionally hard to trade, and take an enormous amount of skill and screen time to recognize the conditions that are likely to produce sustained vs failed ones. What does this logically imply then? That failed breakouts work often. These are my favorite pattern, because it puts you on the side of the institutions, rather than on the side of those chasing breakouts and getting trapped. Markets look for liquidity, and the best way to do that is to wait for a breakdown, trap shorts chasing, grab stops on longs, then reverse. Mastering this setup allows you to benefit from it this effect, rather than be a victim of it like the majority of retail traders.
Institutions have a set bag of tricks they use to enter the market, and the failed breakdown is #1 on that list. If you want to enter with enormous size, you need liquidity, and to find liquidity, you have to hunt for it. Fortunately for institutions, liquidity hides in very predictable spots (and it just so happens to be the spots that most retail traders will either have their stops, or start FOMOing into a short). If you are an institution and want to enter a massive long order, the best way to do it is to use a little capital to flush a major support/cluster of lows, then buy directly from all the stops hiding underneath and all the traders FOMO’ing in thinking the market is going to crash. Then, the market can squeeze higher.
The average retail trader loses because they constantly get trapped. They chase a long based a “breakout” with FOMO, and it reverses, they chase a short based on “breakdown” with FOMO, and it reverses. They put in a stop in, and it gets flushed, then price rallies etc. This is how institutions make money - they seek liquidity, and if you cannot clearly see the trap before you get into a trade, you are probably going to be the one getting trapped. The failed breakdown flips the script here, and exploits liquidity seeking so are you trading alongside the bulk of institutional money after the trap, rather than being victimized by it.
Failed breakdowns involve 4 basic criteria. The more these criteria are maximized, the higher the win rate of the failed breakdown and these setups can vary from a 40% win rate to an 80 or 90% win rate depending on how strongly these criteria are met.:
1) Price must put in a significant low. This is the most important criteria and if this is not present, there is no Failed Breakdown. A significant low is ideally the prior days low and this is the gold standard. Absent this, a major multi-hour low can work, provided it goes 20-30+ points. Absent this, a cluster of lows can also work. It cannot be any random low - the initial low has to produce a rally that is technically significant, strong, and obvious on the chart. When you look on a 15 or 30 minute chart the low should produce a bounce you would’ve liked to trade, and it should ideally run for several levels and defend for several hours. It also does not have to be a single low, and multiple lows connected by a horizontal or even sloped trendline at the same level also works. Remember that a low is a pivot. It is a point where a pullback ends, and a rally begins. It should look like a “V”. If there is no discrete “V”, it may not be a significant low. A good litmus test is if you looked back on the chart a few months from now, the low should jump out. A quick little bounce is not a significant low. If this criteria is met, you can move on to the next criteria.
2) The low should occur at a technically significant spot. Ideally, a major trendline or horizontal support or prior area where price broke out and is now back-testing. It can’t just be any random spot on the chart and must be in an important area that was previously significant if you look to the left of the chart.
3) Price puts in a convincing loss of that low, enough to trap shorts and run stops on longs. It should look quite dramatic in real-time, as if you can see traders being trapped. Ideally, two to eleven points is a good range for how much price should lose the low by and I consider this a gold standard. Generally under 2 points questionable if its a failed breakdown and (this borders just on a test of a level). Over 11 points there is risk its a “real” breakdown. Of course failed breakdowns where price loses the low by over 11 points or under 2 points and recovers happen all the time and they can work fine, but we should know what the ideal is.
4) Price reclaims the low and we can enter. The entry is where the art comes in and this takes screen time to learn. The standard is you want to enter a little above the low (1-3 points but there is no hard rule here), but there is a Golden Rule for Failed Breakdowns. Namely, if you rush into the Failed Breakdown, you lose. We need to wait and the amount we wait is not a function of time, but of structure. Specifically, we need to see what I call acceptance. Acceptance refers to price demonstrating to us that it wants to be at or above the low, rather than trap and head lower. The time acceptance can take varies dramatically. In volatile markets, acceptance can take 2-5 minutes. In slow markets, acceptance can take 2-5hrs.What is the structure we need to see? Acceptance can come in two basic forms. The first form is price back-tests the low, sells off, then returns to the low. After this, we can enter when price rips through the low with force, momentum, and conviction. The second form is price recovers the low rips, sells off to either back-test the low from above or lose the low, then rallies/recovers. They generally look like this:
  

Acceptance is essentially giving price some time to show its cards. It gives price the space and time it needs to trap any impatient traders. After price shows us its hand (by attempting some sort of sell and failing to follow through) then we can enter. Screen time is a must to develop the skill to recognize this because as I said, acceptance can vary dramatically. Deep Failed Breakdowns (think 20-30+ point flushes), often need lots of acceptance. Shallow Failed Breakdowns (10 points) require very little.
As stated above, these 4 conditions are not all mandatory to take a failed breakdown. Markets are probabilistic - there will be failed breakdowns that meet all the criteria perfectly that don’t work. There will be others that meet 1 criteria and result in three day moves. But the probabilities favor the setups that stack most criteria. They are decision making tools.
For this setup, the stop goes several points below the lowest low (perhaps an average 5 points for me). The setup is considered valid until the lowest low of the Failed Breakdown fails. The buffer used will vary depending on context (it is why screen time is invaluable). Below are some examples of how this looks both in its “textbook” variety, and then in real life examples.
  

  

  

  

  

  

  

  

The last chart shows a series of failed breakdowns that occurred Daily from January 5th 2024, to January 10th 2024. This is a good demonstration of how these setups in context, and how they can vary structurally which in turn results in deviations in quality. The only way to internalize this setup, is to see hundreds of them and I try to expedite this process daily by sharing as many as possible in the newsletters.
A Variation of the Failed Breakdown, The Level Reclaim
What is a Level Reclaim then? A Level Reclaim has none of that. A Level Reclaim is when ES recovers a horizontal resistance line or shelf, that often also previously acted as support. I call these “S/R Shelves”. This will typically be a horizontal line which had a touch or two as support, then failed, and thereafter had a few touches as resistance. These can take a wide variety of forms though, but the key defining trait is there needs to be an obvious horizontal line there that has action both above and below it, such as shown in the example below.
Therefore the difference between a Failed Breakdown and Level Reclaim is a Failed Breakdown has a singular low or cluster of lows that flushes rapidly, traps, recovers. A Level Reclaim is when price forms an S/R line, and recovers it. Unlike with Failed Breakdowns, these are much slower. Failed Breakdowns price slingshots below a low and pops above. A Level Reclaim price might lose the line one day, then the next day return to it.
  

They look a little like the above.
Level Reclaims are hard because you need to know how to chart horizontal lines. With Failed Breakdowns you don’t - you just need to be able to spot a significant low. When it comes to entry on Level Reclaims, one can just follow the same requirements as a Traditional Failed Breakdown (wait for acceptance, then long the recovery).
10) A secondary setup of mine is what I call the back-test entry. While price action may seem random and chaotic, it is actually broken down into several fundamental building blocks. One of those building blocks is what I call the breakout-backtest-rally cycle (or conversely, breakdown-backtest-sell). Everyone knows what a breakout is - price moves out of a previously strong level with aggressive speed, momentum, and conviction. After this happens, price very often returns to backtest the level. This does not happen 100% of the time (nothing in markets happens 100% of the time, or even close), but it is a tendency.
When price returns to the breakout level - on the first test - there is a strong tendency for price to rally. On subsequent back-tests, that probability drops successively, until the level eventually caves through. There is no standardization to these probabilities, but as an example the first backtest may hold for a strong move, the second may hold for a strong move, then the third time, price will fail. The bottom line is: When breaks out, it often comes back to backtest the breakout level. On the first test, it is often a good long. Conversely, when price breaks down, it often rallies back to backtest the break down level. On the first test, it is often a good sell. The breakout level can be many things - a horizontal line, a zone, or a trendline. Below are three examples. The first is of a straightforward horizontal level back-test, the second is of a bull flag backtest and this one is a little more nuanced, and the third is of a triangle pattern breakout and back-test.
A backtest trade has 4 steps: 1) A high momentum, strong, obvious breakout 2) A clearly defined zone that price broke out from. It can be a pattern, a trendline, a channel, a zone of messy highs 3) Price needs to come back to retest the zone. 4) Buy the retest on the 1st test. Sometimes it holds on 2nd and 3rd tests as well, but the odds drop.
  

  

  

  

  

11) A third setup of mine is the Breakdown Trade. As a warning, these trades are difficult - they take skill, plenty of screen time, and have a low win rate. When they work though - they payout big. They fail often because most breakdowns are failed breakdowns (which is why failed breakdowns are my top setup). In order to reduce to the risk of being caught in a failed breakdown, a concept known as “acceptance” is required. When price “accepts” a level, it means price has tested the level several times and continues to return to it, which suggests an increasing odds the level will break down in a sustained manner, as opposed to a failed breakdown. Think of it as price magnetizing to the level and eating up existing demand there. Acceptance usually looks like price hitting a support, bouncing off it, testing it again, perhaps trying a failed breakdown, then returning to the level once more. It takes lots of screen time to recognize this dynamic and is more of an art than a science.
When looking to take a trade based on this - patience is the necessary condition. Acceptance takes time and almost always there are many actionable trades *during* the acceptance process. For example, if price hits a support, one could try the long on the first test. It may go down up level for a profit. Then it may return to the level again, and try to break down, resulting in a failed breakdown long-setup. Then, price may return again, indicating acceptance. Only *after* this condition is met does it become a candidate for a breakdown trade on the short side below the entire zone.
The general formula for Breakdown Shorts is as follows is:
1) Identify a clear horizontal level or cluster of lows that you want to short below. It should be obvious, and well defined. The Breakdown Short requires “something” to break. If there is nothing clear to break, there is no short. This is usually a clear multi-touch horizontal support.
2) Wait for a bounce and/or failed breakdown at the zone immediately before you short. You cannot just rush into breakdown shorts or you will get trapped. What is the best way to avoid getting caught in a failed breakdown? Have one already having taken place at the level of interest. If no failed breakdown is present, a bounce at the zone is also sufficient, but you want to see one or the other *immediately prior* to shorting.
3) After this, you can place a short trigger a several point buffer below all the noise. Your short trigger should go in a fresh area that has not been tested yet, away from the all the noise to its left.
  

We can see in the above diagram all the pieces: An obvious support level, a bounce and/or Failed Breakdown at the level immediately prior to shorting, then one places the trigger beneath all the structure at the zone. Obviously, they won’t always look like the above diagram, but this is the archetypal example to demonstrate the general concept of seeing bounces and/or Failed Breakdowns *before* shorting.
Very important: These types of trades represent a small % of my overall trades (under 10%), and are also the lowest win rate of the types of trades I do. These trades work best in very strongly up-trending markets, or very strongly down-trending markets. This type of trade takes tons of screen time and skill to execute well, and you can expect a “high cost of business” especially when learning this. For experienced traders though, this can be a very lucrative tool to add to the arsenal (because when they do work, they work in a massive way). For brand new traders, I would recommend starting with the failed breakdown/breakout, discussed above.
Below are two images of the concept in reverse for a Breakout Trade. I rarely take these and mostly just do Breakdown Shorts, but the concept is the same. The first shows an idealized example of an acceptance base, followed by a breakout to trigger longs. The subsequent images show real-life examples of Breakdown Shorts.
  

  

  


  

12) There are two basic market modes, which I call “Mode 1” and “Mode 2”. This is the fundamental framework through which I interpret price action, and the market is an endlessly cycling sequence between “Mode 1” and “Mode 2”
Mode 1=trend. These days are rare, compromising under 15% of price action. Mode one is defined by one side clearly in control, counter-trend moves are shallow, and price cleanly moves multiple levels. New traders look for Mode 1 action everyday, and this is a major mistake, as it is rare.
Mode 2=range bound action. This is most of the price action. In Mode 2, price picks a range, and bulls and bears are generally in balance, with one side typically slightly favored depending on the underlying trend. Mode 2 is characterized by traps, lack of follow through, and indecision - the exact opposite of Mode 1. In Mode 2, failed breakdowns are your best friend, as price loves to fake above and below the range to trap traders. Your worst enemy in Mode 2 is over-trading, and specifically what I call “flipping” which is a classic losing retail trader behavior where a trader gets long, perhaps gets stopped out, flips short, gets stopped out again etc. In mode 2, you pick your spots, wait for failed breakdowns, give them space - never flip back and forth - then get out when you get your level to level move.
All Mode 2 action forms a classical chart pattern, and markets endlessly cycle between this. Mode 1 trend, leads to Mode 2 chop/pattern formation, which leads to Mode 1 trend. A classic example would be a large rally (Mode 1), followed by a bull flag (Mode 2), followed by another rally (Mode 1). Or alternatively, a large selloff (Mode 1), followed by an inverse head and shoulders (Mode 2), followed by a rally (Mode 1).
13) My guide for trading chop conditions can be found in this newsletter in the bolded section
Some Final Points On Using The Newsletter + Trading
14) Important: On occasion, I will mention possible “entry triggers” in this newsletter. Note that like everything in markets, these have probabilities attached. An entry trigger does not mean that it will trigger then the trade will follow through perfectly and instantly 100% of the time, with no intra trade drawdown. It means that I think a good 65-75% of the time such an entry would see follow through to next level *if given space to work*. Nothing in markets works anywhere remotely close to 100% of the time, and hedge funds spend billions of dollars to grind out edges that work 51% of the time. If you are looking for something that does work 100% of the time (or even 80% of the time is extremely optimistic and extraordinarily rare), I would not recommend trading or other probability based games that are based on exploiting small edges over large samples.
15) I include a “trade recap” in each newsletter. This has become just a sort of “trade journal for me” and I include it for educational purposes only. My main goal with it is simply to show the imperfection of even generally profitable trading - I miss most moves, the moves I catch I usually catch only fractions, and I am getting stopped out of runners almost every day. Profitable trading is not perfect.
16) There's no shortcut for thousands of hours of screen time. Good resources certainly expedite the learning curve significantly and it is absolutely essential to have a process that has a decisive edge, but until you've seen that edge in all contexts: Uptrends, downtrends, chop, failed examples, successful examples and workshopped the execution, you won't max its win-rate. No way to bypass hard work
17) Final point - be a trader, not a gambler. On my wall in my office I have my job description as a trader written down, and I have had it there for a decade. 1) Identify setups in the plan the night before. Where to trade and under what conditions 2) Wait on price to enter the zones and present the conditions I planned. Do not enter unless I feel 100% conviction. Do not chase price, wait for price to come to me 3) Take the trade level to level. Manage it like an algorithm with 0 variation - 75% profits level 1, more profits level 2, leave a runner 4) Do not go back red on the day, ever, under any circumstance. If a second trade is attempted, make it partial size. 5) No FOMO, no over-trading. The job is not to catch every move, but the portion of the daily action that corresponds to my edge. This may be 5 points, it may be 100 points. Sidelines the rest, and this leads to massive wealth compounding over time.