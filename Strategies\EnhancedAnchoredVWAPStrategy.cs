using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.Core;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using System.Windows.Media;
using NinjaTrader.NinjaScript.DrawingTools;

using System.Reflection; // <<< ADDED
using System.Text;       // <<< ADDED
using NinjaTrader.Gui.Tools; // For NinjaScriptProperty attribute

using AnchoredVWAPIndicator = NinjaTrader.NinjaScript.Indicators.AnchoredVWAP;

namespace NinjaTrader.NinjaScript.Strategies
{
    [Description("Enhanced Anchored VWAP Strategy with Dynamic Anchor and Entry Switching based on <PERSON>'s concepts")]
    public class EnhancedAnchoredVWAPStrategy : Strategy
    {
        #region Strategy Parameters
        [NinjaScriptProperty]
        [Display(Name = "Anchor Type", Description = "Type of anchor for AVWAP (overridden by dynamic mode)", Order = 1, GroupName = "AVWAP Settings")]
        public NinjaTrader.NinjaScript.AnchorTypeEnum AnchorTypeSelection { get; set; } = NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar;

        [NinjaScriptProperty]
        [Display(Name = "Volume Threshold Multiplier", Description = "Multiplier for HighVolumeBar detection (x SMA)", Order = 2, GroupName = "AVWAP Settings")]
        [Range(1.5, 5.0)]
        public double VolumeThresholdMultiplier { get; set; } = 2.0;

        [NinjaScriptProperty]
        [Display(Name = "Gap Threshold Percent", Description = "Min gap size for GapOpening detection (%)", Order = 3, GroupName = "AVWAP Settings")]
        [Range(0.5, 5.0)]
        public double GapThresholdPercent { get; set; } = 1.0;

        [NinjaScriptProperty]
        [Display(Name = "Entry Method", Description = "Entry method (overridden by dynamic mode)", Order = 4, GroupName = "Entry Settings")]
        public EntryMethod EntryMethodSelection { get; set; } = EntryMethod.Momentum;

        [NinjaScriptProperty]
        [Display(Name = "Use Multi-Timeframe Analysis", Description = "Enable multi-timeframe analysis", Order = 5, GroupName = "Entry Settings")]
        public bool UseMultiTimeframeAnalysis { get; set; } = false;

        [NinjaScriptProperty]
        [Display(Name = "Enable AVWAP Pinch Detection", Description = "Detect and trade AVWAP pinch patterns", Order = 6, GroupName = "Entry Settings")]
        public bool EnablePinchDetection { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "Enable Handoff Detection", Description = "Detect and trade AVWAP handoff patterns", Order = 7, GroupName = "Entry Settings")]
        public bool EnableHandoffDetection { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "Max AVWAP Slope for Entry", Description = "Max slope for touch trades", Order = 8, GroupName = "Risk Management")]
        [Range(0.05, 0.3)]
        public double MaxAVWAPSlope { get; set; } = 0.15;

        [NinjaScriptProperty]
        [Display(Name = "Stop Distance in ATR", Description = "Initial stop-loss distance (ATR)", Order = 9, GroupName = "Risk Management")]
        [Range(0.5, 2.0)]
        public double StopDistanceATR { get; set; } = 1.3; // ✅ REVERT: Back to original working value

        [NinjaScriptProperty]
        [Display(Name = "Profit Target in ATR", Description = "Profit target (ATR)", Order = 10, GroupName = "Risk Management")]
        [Range(1.0, 5.0)]
        public double ProfitTargetATR { get; set; } = 3.0; // ✅ CONFIRMED: Keep original 3.0 ATR target that works

        [NinjaScriptProperty]
        [Display(Name = "Enable Logging", Description = "Enable trade/debug logging", Order = 11, GroupName = "Settings")]
        public bool EnableLogging { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "Debug Mode", Description = "Enable verbose debug output and visualizations", Order = 12, GroupName = "Settings")]
        public bool DebugMode { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "Max Daily Loss", Description = "Max daily loss ($)", Order = 13, GroupName = "Risk Management")]
        [Range(100, 5000)]
        public double MaxDailyLoss { get; set; } = 1000;

        [NinjaScriptProperty]
        [Display(Name = "Max Risk Per Trade", Description = "Max risk per trade ($)", Order = 14, GroupName = "Risk Management")]
        [Range(100, 5000)]
        public double MaxRiskPerTrade { get; set; } = 750; // Back to original baseline

        [NinjaScriptProperty]
        [Display(Name = "Min ADX for Entry", Description = "Min ADX for entry", Order = 16, GroupName = "Filters")]
        [Range(15.0, 40.0)]
        public double MinADXForEntry { get; set; } = 28;
        // Unique file set ID for tying together logs and summary
        private string fileSetID;

        [NinjaScriptProperty]
        [Display(Name = "Min Volume Ratio", Description = "Min volume ratio (current volume / SMA volume)", Order = 17, GroupName = "Filters")]
        [Range(1.0, 1.5)]
        public double MinVolRatio { get; set; } = 1.0;

        [NinjaScriptProperty]
        [Display(Name = "Enable MFE Trail", Description = "Trail stop using Maximum Favorable Excursion with ATR-based trailing", Order = 18, GroupName = "Risk Management")]
        public bool EnableMfeTrailInternal { get; set; } = true;

        [NinjaScriptProperty]
        [Display(Name = "MFE Peak Trail ATR", Description = "ATR multiplier for trailing stop distance from peak price", Order = 18, GroupName = "Risk Management")]
        [Range(0.5, 3.0)]
        public double MFEPeakTrailATR { get; set; } = 1.5; // ✅ OPTIMIZATION: Looser ATR trail for better capture

        [NinjaScriptProperty]
        [Display(Name = "Minimum Pinch Width %", Description = "Minimum width for a pinch to be valid (%)", Order = 19, GroupName = "Pinch Settings")]
        [Range(0.1, 2.0)]
        public double MinPinchWidthPercent { get; set; } = 0.5;

        [NinjaScriptProperty]
        [Display(Name = "Min Time After Pinch (min)", Description = "Minimum time after pinch before trading", Order = 20, GroupName = "Pinch Settings")]
        [Range(5, 60)]
        public int MinTimeAfterPinchMinutes { get; set; } = 15;

        [NinjaScriptProperty]
        [Display(Name = "AVWAP Period", GroupName = "AVWAP Settings", Order = 21)]
        [Range(10, 50)]
        public int AVWAPPeriod { get; set; } = 22;

        [NinjaScriptProperty]
        [Display(Name = "Use StdDev Bands", GroupName = "AVWAP Settings", Order = 22)]
        public bool UseStdDevBands { get; set; } = false;

        [NinjaScriptProperty]
        [Display(Name = "Number of StdDevs", GroupName = "AVWAP Settings", Order = 23)]
        [Range(0.5, 3.0)]
        public double NumStdDev { get; set; } = 3.0;

        [NinjaScriptProperty]
        [Display(Name = "Use Dynamic Mode", Description = "Enable dynamic anchor and entry selection", GroupName = "Advanced", Order = 99)]
        public bool UseDynamicMode { get; set; } = false;

        [NinjaScriptProperty]
        [Display(Name = "Dynamic Update Interval (min)", Description = "Interval for dynamic anchor/entry updates", GroupName = "Advanced", Order = 100)]
        [Range(1, 60)]
        public int DynamicUpdateIntervalMinutes { get; set; } = 5;

        [NinjaScriptProperty]
        [Display(Name = "Filter Low Volume Days", Description = "Skip trading on low volume days", GroupName = "Advanced", Order = 101)]
        public bool FilterLowVolDays { get; set; } = false;

        [NinjaScriptProperty]
        [Display(Name = "Export Signals to CSV", Description = "Export trade signals to CSV for analysis", GroupName = "Advanced", Order = 102)]
        public bool ExportSignals { get; set; } = false;

        [NinjaScriptProperty]
        [Display(Name = "Analyze Target Hits", GroupName = "Debug", Order = 90)]
        public bool AnalyzeTargetHits { get; set; } = true;

        private string csvSummaryPath; // For CSV summary output (removed readonly for runtime assignment)
        private static readonly object csvFileLock = new object(); // For thread safety
        private string runID; // Unique run/parameter set ID for summary and trade logs
        #endregion

        #region Enums and Constants
        private const double POINT_VALUE = 5.0;
        private const int WARMUP_BARS = 20;
        private const double trailPct = 0.85; // Percentage of MFE to trail (15% pullback = 85% capture)
        private readonly string signalExportPath = Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs", "EnhancedAVWAP", "Signals.csv");

        public enum EntryMethod
        {
            Touch,
            Momentum,
            SecondDay,
            PinchBreakout,
            GreenToRed,
            RedToGreen,
            Handoff
        }

        public enum TimeframeAlignment
        {
            Aligned,
            Divergent,
            Neutral
        }
        #endregion

        #region Variables
        private readonly object orderLock = new object();
        private List<Execution> allExecutions = new List<Execution>();
        private double totalCommission = 0.0;
        private double instrumentPointValue;
        private double lastExitPnL = 0.0; // Track PnL for Exit logging
        private bool exitOrdersPlaced = false; // Flag to track if bracket orders have been placed
        private int intendedEntrySize = 0; // Track intended position size for partial fills
        private int totalFilledQuantity = 0; // Track cumulative filled quantity for partial fills

        private LogManager logManager;
        private TradeManager tradeManager;
        private StatsTracker statsTracker;
        // TimeframeManager removed

        private ATR atr14;
        private ATR atr1min;
        private ADX adx14;
        private SMA smaVolume;
        private AnchoredVWAPIndicator avwap;
        private AnchoredVWAPIndicator avwapHighVol;
        private AnchoredVWAPIndicator avwapPriorDay;
        private AnchoredVWAPIndicator avwapPriorHigh;
        private AnchoredVWAPIndicator avwapPriorLow;
        private AnchoredVWAPIndicator avwapSecondDay;
        private Series<double> avwapSlope;
        private SessionIterator sessionIterator;

        private DateTime currentSessionDate = DateTime.MinValue;
        private DateTime currentTimeLocal;
        private DateTime sessionStartTime;
        private DateTime sessionEndTime;
        private bool dailyStopHit;
        private bool sessionEndExitLogged;
        private bool entrySubmitted = false; // 🚫 prevents repeated orders per signal
        private int dailyTradeCount;
        private bool anchorLogged;

        private bool isFirstDayOfMove;
        private bool isSecondDayOfMove;
        private bool isThirdDayOfMove;
        private double priorDayClose;
        private double priorDayHigh;
        private double priorDayLow;
        private double priorDayOpen;
        private double dayStartPrice;
        private double secondDayOpenPrice;
        private DateTime priorHighVolumeDay;

        private bool isPinchFormed;
        private double pinchWidth;
        private DateTime pinchFormationTime;
        private double upperPinchValue;
        private double lowerPinchValue;

        private bool handoffDetected;
        private DateTime handoffTime;
        private double handoffPrice;
        private bool waitingForHandoffEntry;

        private List<double> dayHighs = new List<double>();
        private List<double> dayLows = new List<double>();
        private List<double> dayCloses = new List<double>();
        private List<double> dayOpens = new List<double>();
        private List<double> dayVolumes = new List<double>();
        private List<DateTime> dayDates = new List<DateTime>();
        private MarketPosition lastPosition = MarketPosition.Flat;
        private int lastHandoffBar = -1;

        // MFE Trail variables
        private double highestUnrealizedPnL = 0.0;
        private bool mfeTrailArmed = false;
        private bool mfeTrailLogArmedPrinted = false;
        private double armThreshold = 0.0;
        private double frozenMFEOnArm = 0.0; // ✅ NEW: Snapshot maxPnL at arming for frozen trail trigger
        private Order nativeStopLoss = null; // ✅ DIFF 68: Store native stop order reference for cancellation
        private string currentSignalName = string.Empty; // Store current signal name for MFE trail exit
        private string mfeTrailLogPath = string.Empty;
        private readonly object mfeTrailLogLock = new object();
        private bool EnableMFETrail => EnableMfeTrailInternal;
        private DateTime mfeTrailArmedTime = Core.Globals.MinDate;
        private double mfeTrailArmedPrice = 0;
        private double mfeTrailMaxPnL = 0;

        // ✅ DIFF 1: Add EntryTime mapping for reliable timestamp reconciliation
        private Dictionary<string, DateTime> tradeIdToEntryTimeMap = new Dictionary<string, DateTime>();

        // ✅ DIFF 6: Throttling variables to prevent log spam
        private string lastLoggedResetTradeID = string.Empty;
        private string lastLoggedRecoverySignal = string.Empty;
        // Removed unused stopOrder reference

        // 3-phase MFE trail model variables
        private double initialStopPrice = 0.0;
        private double breakevenPrice = 0.0;
        private double trailingStopPrice = 0.0;
        private bool breakevenStopActive = false;
        private bool atrTrailActive = false;
        private bool mfeExitSubmitted = false;
        private bool exitSubmitted = false;
        private Dictionary<string, string> tradeIdMap = new Dictionary<string, string>();
        private Dictionary<string, string> tradeIdToSignalMap = new Dictionary<string, string>(); // ✅ DIFF 32: Store signal names per trade ID
        private Dictionary<string, double> tradeIdToMaxPnLMap = new Dictionary<string, double>(); // ✅ DIFF 32: Store maxPnL per trade ID
        private string lastTradeID = "";
        private bool logWritten = false;
        // ✅ DIFF 43: Removed obsolete deduplication flags — replaced by mfeTrailLoggedTradeIds
        // ✅ DIFF 36: Unified TradeID and Logging Enforcement
        private string currentTradeID = null; // Changed to null for cleaner initialization
        private HashSet<string> mfeTrailLoggedTradeIds = new HashSet<string>(); // ✅ DIFF 28: Centralized logging deduplication
        private string lastMfeTrailExitReason = ""; // ✅ DIFF 28: Track last MFE trail exit reason
        private bool mfeTrailHeaderWritten = false; // ✅ DIFF 34: Track if MFE trail header has been written
        private bool sessionCloseTriggered = false; // ✅ DIFF 36: Track session end triggered exits
        private bool trailExitSubmitted = false;
        private bool exitLogged = false;
        private bool mfeTrailShouldHaveLogged = false; // ✅ DIFF 44: Track if trail should have logged

        // ✅ DIFF 36: Fallback MFE Trail Logging Guard
        private void CheckFinalTrailLoggingFallback(string tradeId, string reason, double finalPnL)
        {
            if (string.IsNullOrEmpty(tradeId) || mfeTrailLoggedTradeIds.Contains(tradeId))
                return;

            // Get trade details for fallback logging
            Trade lastTrade = SystemPerformance.AllTrades.Count > 0 ? SystemPerformance.AllTrades.Last() : null;
            if (lastTrade != null && lastTrade.Entry != null && lastTrade.Exit != null)
            {
                DateTime entryTime = tradeIdToEntryTimeMap.ContainsKey(tradeId)
                    ? tradeIdToEntryTimeMap[tradeId]
                    : lastTrade.Entry.Time;

                double maxMFE = tradeIdToMaxPnLMap.ContainsKey(tradeId)
                    ? tradeIdToMaxPnLMap[tradeId]
                    : (tradeManager?.MaxFavorableExcursion ?? 0);

                WriteMFETrailLogOnce(
                    tradeId,
                    currentSignalName,
                    lastTrade.Entry.Price,
                    lastTrade.Exit.Price,
                    entryTime,
                    lastTrade.Exit.Time,
                    maxMFE,
                    finalPnL,
                    reason + "_FinalFallback",
                    true); // wasOverride = true for fallback

                Print($"[FALLBACK_LOG] Trade {tradeId} logged via fallback mechanism. Reason: {reason}");
            }
        }

        // Exit attribution variables
        private string lastExitReason = string.Empty;

        // PnL Debug logging throttling variables
        private double lastLoggedManualPnL = double.MinValue;
        private double lastLoggedClose = double.MinValue;

        // Track highest equity across the entire strategy run
        private double overallPeakEquity = 0;
        private bool cumulativeStopHit = false; // Flag for cumulative intra-trade stop

        // ✅ DIFF 30: Entry bar guard to prevent immediate stopouts
        private int entryBarIndex = -1;
        private int entryBarIndex1Min = -1;

        // ✅ DIFF 31: Real risk & stop hardening layer
        private double entryPrice = 0.0;
        private double cachedEntryPrice = 0.0;
        private DateTime cachedEntryTime;
        private bool isTradeActive = false;
        private bool entryCapturedFallback = false;
        private double minATR = 2.0; // hard floor in points

        // ✅ DIFF 80: Implement optimal hard stop based on SessionEnd loss analysis
        private double hardStopDist = 160.0; // Points away = $800 max risk (160 * $5/point)

        // ✅ MAJOR OPTIMIZATION: Make PnL trail a "disaster prevention" mechanism only
        // $400 arm threshold (proven), 40% trail capture - let ATR trail be primary manager
        // Target: Convert $9,637 in lost profits to captured profits via superior ATR trail
        private double mfeTrailExitThreshold = 0.40; // ✅ MAJOR FIX: 40% capture (60% pullback) - PnL trail as safety net only
        private double mfeTrailArmThreshold => 400.0; // ✅ KEEP: Proven $400 threshold

        // ✅ DIFF 54: Simplified MFE Trail with Low[0]/High[0] intra-bar execution
        private double mfeTrailStopPrice = 0.0; // Trail stop price
        private double MfeTrailArmThreshold => mfeTrailArmThreshold; // Alias for compatibility
        private double MfeTrailStopAtrMultiplier => MFEPeakTrailATR; // Use existing ATR multiplier

        // ✅ DIFF 35.3: Price-Based MFE Trail Exit (75% Capture from Peak)
        private double pointValue = 5.0; // For MES — update if needed

        // ✅ DIFF 4A: Unified stop distance for consistent risk management
        private double currentStopDistance = 0.0; // Backsolved stop distance based on position size and MaxRiskPerTrade
        #endregion

        #region Manual Exit Methods
        /// <summary>
        /// Unified exit controller - handles all exit types with proper order management
        /// </summary>
        private void SubmitExit(string reason, double? overridePrice = null)
        {
            if (exitLogged || Position.MarketPosition == MarketPosition.Flat)
            {
                Print($"[SubmitExit] BLOCKED | exitLogged={exitLogged} | pos={Position.MarketPosition} | reason={reason}");
                return;
            }

            Print($"[SubmitExit] EXECUTING | reason={reason} | pos={Position.MarketPosition} | overridePrice={overridePrice?.ToString("F2") ?? "market"}");

            // 1) Submit the exit first (managed), then clear brackets to avoid naked windows if cancel races
            if (overridePrice.HasValue)
            {
                if (Position.MarketPosition == MarketPosition.Long)
                    ExitLongLimit(0, true, Position.Quantity, overridePrice.Value, $"Exit_{reason}", currentSignalName);
                else if (Position.MarketPosition == MarketPosition.Short)
                    ExitShortLimit(0, true, Position.Quantity, overridePrice.Value, $"Exit_{reason}", currentSignalName);
            }
            else
            {
                if (Position.MarketPosition == MarketPosition.Long)
                    ExitLong($"Exit_{reason}", currentSignalName);
                else if (Position.MarketPosition == MarketPosition.Short)
                    ExitShort($"Exit_{reason}", currentSignalName);
            }

            // 2) Now cancel any active bracket orders to prevent conflicts
            if (tradeManager != null)
            {
                tradeManager.CancelActiveOrders();
                Print($"[SubmitExit] Active orders canceled AFTER exit submitted | Reason: {reason}");
            }

            // Also cancel native stop loss if it exists (post-exit submission)
            if (nativeStopLoss != null && nativeStopLoss.OrderState == OrderState.Working)
            {
                CancelOrder(nativeStopLoss);
                Print($"[SubmitExit] Native stop loss canceled after exit submission | Reason: {reason}");
                nativeStopLoss = null;
            }

            // Mark exit as submitted and store reason
            exitLogged = true;
            lastMfeTrailExitReason = reason;
            trailExitSubmitted = true;

            Print($"[SubmitExit] EXIT SUBMITTED | reason={reason} | pos={Position.MarketPosition} | time={Time[0]:HH:mm:ss}");
        }

        /// <summary>
        /// Legacy manual exit method for MFE trail exits - now uses unified SubmitExit
        /// </summary>
        private void ExitPosition(string reason = "", bool force = false)
        {
            if (Position.MarketPosition == MarketPosition.Flat)
                return;

            if (exitLogged && !force)
                return;

            // Delegate to unified exit controller
            SubmitExit(reason);
        }
        #endregion

        #region OnStateChange
        protected override void OnStateChange()
        {
            // Replace the entire SetDefaults block
            if (State == State.SetDefaults)
            {
                Description = "Enhanced Anchored VWAP Strategy with Dynamic Anchor and Entry Switching based on Brian Shannon's concepts";
                Name = "EnhancedAnchoredVWAPStrategy";
                Calculate = Calculate.OnEachTick;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                BarsRequiredToTrade = WARMUP_BARS;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0; // Match settings.1.csv
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Day;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                IsInstantiatedOnEachOptimizationIteration = true;
                // Set IsUnmanaged to false to allow use of EnterLong(), EnterShort(), etc.
                IsUnmanaged = false;

                // Set defaults to match your backtest parameters
                AnchorTypeSelection = NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar; // Match settings.1.csv
                VolumeThresholdMultiplier = 2.0;
                GapThresholdPercent = 1.0;
                EntryMethodSelection = EntryMethod.GreenToRed; // Match settings.1.csv
                UseMultiTimeframeAnalysis = false;
                EnablePinchDetection = true;
                EnableHandoffDetection = true;
                MaxAVWAPSlope = 0.15;
                StopDistanceATR = 1.3; // ✅ CONFIRMED: Original working value
                ProfitTargetATR = 3.0; // ✅ CONFIRMED: Original 3.0 ATR target that works
                EnableLogging = true;
                MaxDailyLoss = 1000;
                MaxRiskPerTrade = 800; // ✅ DIFF 80: Optimal risk based on SessionEnd loss analysis
                MinADXForEntry = 28;
                MinVolRatio = 1.0; // Match settings.1.csv
                MinPinchWidthPercent = 0.5;
                MinTimeAfterPinchMinutes = 15;
                AVWAPPeriod = 22; // Match settings.1.csv
                UseStdDevBands = false;
                NumStdDev = 3.0; // Match settings.1.csv
                UseDynamicMode = true;
                DynamicUpdateIntervalMinutes = 5;
                FilterLowVolDays = false;
                ExportSignals = false;
            }
            else if (State == State.Configure)
            {
                // 🔥 Force delete existing MFETrailLogs.csv to ensure clean log each session
                string mfeLogPath = System.IO.Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs", "EnhancedAVWAP", "MFETrailLogs.csv");
                if (System.IO.File.Exists(mfeLogPath))
                {
                    try
                    {
                        System.IO.File.Delete(mfeLogPath);
                        Print("[LOG INIT] Previous MFETrailLogs.csv deleted.");
                    }
                    catch (Exception ex)
                    {
                        Print($"[ERROR] Could not delete MFETrailLogs.csv: {ex.Message}");
                    }
                }

                if (EnableLogging)
                {
                    // Generate unique run ID and file set ID at the start of Configure
                    runID = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    fileSetID = $"{runID}_{Guid.NewGuid():N}";
                    logManager = new LogManager(this, fileSetID);
                    logManager.InitializeLogs();
                    // --- CSV Summary Path Initialization ---
                    try
                    {
                        if (Bars != null && Bars.Count > 0)
                        {
                            string userDocsFolder = NinjaTrader.Core.Globals.UserDataDir;
                            string logSubFolder = "EnhancedAVWAP"; // Hardcoded to match other log files
                            string basePath = Path.Combine(userDocsFolder, "logs", logSubFolder);
                            Directory.CreateDirectory(basePath);

                            // Use fileSetID for unique summary CSV file name
                            string csvFileName = $"StrategySummary_{fileSetID}.csv";
                            csvSummaryPath = Path.Combine(basePath, csvFileName);

                            logManager.Log("[INFO] State.Configure: CSV Base Path constructed: " + basePath, "INFO", "Init");
                            logManager.Log("[INFO] State.Configure: Directory ensured: " + basePath, "INFO", "Init");
                            logManager.Log("[INFO] State.Configure: CSV Full Path configured: " + csvSummaryPath, "INFO", "Init");
                        }
                        else
                        {
                            logManager.Log("[WARN] Bars not yet ready in State.Configure, skipping CSV timestamp logging", "WARN", "Init");
                        }
                    }
                    catch (Exception ex)
                    {
                        logManager.Log("[ERROR] State.Configure: Failed to configure CSV summary path: " + ex.Message, "ERROR", "Init");
                        logManager.Log("StackTrace: " + ex.StackTrace, "ERROR", "Init");
                        csvSummaryPath = null;
                    }
                    // --- END CSV Summary Path Initialization ---

                    // MFE Trail CSV header now handled dynamically by WriteMFETrailLogHeaderIfNeeded()
                }

                AddDataSeries(BarsPeriodType.Minute, 1);
                if (UseMultiTimeframeAnalysis)
                {
                    AddDataSeries(BarsPeriodType.Minute, 5);
                    AddDataSeries(BarsPeriodType.Minute, 15);
                    AddDataSeries(BarsPeriodType.Day, 1);
                }
                IsDataSeriesRequired = true;

                // Removed SetStopLoss call to give full control to MFE trail logic
            }
            else if (State == State.DataLoaded)
            {
                instrumentPointValue = Instrument?.MasterInstrument?.PointValue ?? 5.0;
                sessionIterator = new SessionIterator(Bars ?? throw new Exception("Bars collection is null in State.DataLoaded"));
                tradeManager = new TradeManager(this);
                statsTracker = new StatsTracker(this);
                // TimeframeManager initialization removed

                // Reset logged exits to prevent cross-session collisions
                mfeTrailLoggedTradeIds.Clear(); // ✅ DIFF 28: Clear centralized logging deduplication
                lastMfeTrailExitReason = ""; // ✅ DIFF 28: Reset last exit reason
                mfeTrailHeaderWritten = false; // ✅ DIFF 34: Reset header flag for new session

                // ✅ DIFF 36: Unified TradeID and Logging Enforcement - Reset trade state in DataLoaded
                currentTradeID = null;
                currentSignalName = string.Empty;
                exitLogged = false;
                trailExitSubmitted = false;

                avwapSlope = new Series<double>(this);

                if (Bars?.Count > 0 && CurrentBars[0] >= 0)
                {
                    try
                    {
                        DateTime firstBarTradingDay = sessionIterator.GetTradingDay(BarsArray[0].GetTime(0));
                        ResetDailyParameters(firstBarTradingDay);
                    }
                    catch (Exception ex)
                    {
                        Print($"Error during initial ResetDailyParameters call in DataLoaded: {ex.Message}");
                    }
                }
                else
                {
                    Print("Warning: Could not perform initial ResetDailyParameters call in DataLoaded due to insufficient Bars data.");
                    currentSessionDate = DateTime.MinValue;
                }

                if (ExportSignals && !File.Exists(signalExportPath))
                {
                    try
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(signalExportPath));
                        File.WriteAllText(signalExportPath, "Timestamp,BarNumber,EntryMethod,Price,PositionSize,StopPrice,TargetPrice,AVWAPValue,Slope,ATR,ADX,VolumeRatio,GapPercent,PinchFormed,HandoffDetected,MTFAlignment\n");
                    }
                    catch (Exception ex)
                    {
                        Print($"Error creating signal export file/directory: {ex.Message}");
                        ExportSignals = false;
                    }
                }

                logManager?.Log($"[Info] Data Loaded: Bars={BarsArray[0]?.Count ?? 0}, EntryMethod={EntryMethodSelection.ToString()}", "Info");

                // --- Init fallback for armThreshold in case of pre-trade edge cases ---
                armThreshold = MaxRiskPerTrade * 0.75;
                Print($"[Init] armThreshold initialized in DataLoaded to: {armThreshold:F2}");

                try
                {
                    atr14 = ATR(Close, 14);
                    adx14 = ADX(14);
                    smaVolume = SMA(Volume, 10);

                    if (BarsArray.Length > 1 && BarsArray[1] != null)
                    {
                        atr1min = ATR(BarsArray[1], 14);
                        Print($"[Info] 1-minute ATR initialized for MFE Trail");
                    }
                    else
                    {
                        Print("Error: atr1min not initialized. MFE trail disabled.");
                    }

                    logManager?.Log($"[Info] Standard Indicators Initialized in DataLoaded", "Info");
                }
                catch (Exception ex)
                {
                    Print($"[Error] Failed to initialize standard indicators in DataLoaded: {ex.Message}");
                }

                try
                {
                    DateTime initialAnchorTime = (Bars != null && Bars.Count > 0 && CurrentBars[0] >= 0 && BarsArray[0].Count > 0) ? BarsArray[0].GetTime(0) : DateTime.MinValue;

                    avwap = AnchoredVWAP(AnchorTypeSelection, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    avwapHighVol = AnchoredVWAP(NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    avwapPriorDay = AnchoredVWAP(NinjaTrader.NinjaScript.AnchorTypeEnum.PreviousDayClose, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    avwapPriorHigh = AnchoredVWAP(NinjaTrader.NinjaScript.AnchorTypeEnum.SessionOpen, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    avwapPriorLow = AnchoredVWAP(NinjaTrader.NinjaScript.AnchorTypeEnum.SessionOpen, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    avwapSecondDay = AnchoredVWAP(NinjaTrader.NinjaScript.AnchorTypeEnum.SessionOpen, VolumeThresholdMultiplier, GapThresholdPercent, initialAnchorTime, AVWAPPeriod, 5, UseStdDevBands, NumStdDev, false, false, DebugMode, false);
                    logManager?.Log($"[Info] AVWAPs Initialized in DataLoaded", "Info");
                }
                catch (Exception ex)
                {
                    Print($"[Error] Failed to initialize AVWAPs in DataLoaded: {ex.Message}");
                }
            }
            else if (State == State.Terminated)
            {
                if (EnableLogging && SystemPerformance.AllTrades.Count > 0 && logManager != null)
                {
                    WriteFinalSummary();
                }
                if (logManager != null)
                {
                    logManager.Close();
                }
            }
        }
        #endregion

        #region Core Logic
        protected override void OnBarUpdate()
        {
            // Process 5-minute series for entries and core logic, 1-minute for fills
            if (BarsInProgress == 0)
            {
                if (CurrentBars[0] < BarsRequiredToTrade) return;

                if (UseMultiTimeframeAnalysis && (CurrentBars.Length <= 3 || CurrentBars[1] < 5 ||
                    CurrentBars[2] < 5 || CurrentBars[3] < 1)) return;

                // ✅ DIFF 47: Final fallback for MFE Trail Logging — occurs on bar close after flat
                if (Position.MarketPosition == MarketPosition.Flat && tradeManager != null)
                {
                    // Don't double log and only log MFE exits with arming
                    if (!exitLogged && mfeTrailShouldHaveLogged && !string.IsNullOrEmpty(currentTradeID))
                    {
                        if (!mfeTrailLoggedTradeIds.Contains(currentTradeID))
                        {
                            Print($"[MFE Trail] ✅ Fallback trail log from OnBarUpdate post-flat: {Time[0]} | TradeID: {currentTradeID}");

                            // Use stored execution time for accurate entry timestamp
                            DateTime entryTime = tradeIdToEntryTimeMap.ContainsKey(currentTradeID)
                                ? tradeIdToEntryTimeMap[currentTradeID]
                                : Time[0];

                            // Get the max MFE from our tracking
                            double maxMFE = tradeIdToMaxPnLMap.ContainsKey(currentTradeID)
                                ? tradeIdToMaxPnLMap[currentTradeID]
                                : tradeManager?.MaxFavorableExcursion ?? 0;

                            WriteMFETrailLogOnce(
                                currentTradeID,
                                currentSignalName,
                                cachedEntryPrice > 0 ? cachedEntryPrice : Position.AveragePrice,
                                Close[0],
                                entryTime,
                                Time[0],
                                maxMFE,
                                0, // Final PnL will be calculated in WriteMFETrailLogOnce
                                lastMfeTrailExitReason.IsNullOrEmpty() ? "FinalFallbackBarClose" : lastMfeTrailExitReason,
                                true); // wasOverride = true for fallback

                            exitLogged = true;
                            mfeTrailShouldHaveLogged = false;
                        }
                    }
                }

                if (CurrentBar == BarsRequiredToTrade)
                    logManager?.Log($"[Debug][InitCheck] Bar:{CurrentBar} DebugMode:{DebugMode} LogManagerNotNull:{logManager != null}", "Debug");

                currentTimeLocal = Time[0];
                DateTime currentTradingDay = sessionIterator.GetTradingDay(currentTimeLocal);

                if (currentSessionDate != currentTradingDay)
                {
                    SavePriorDayData();
                    ResetDailyParameters(currentTradingDay);
                    DetectMultiDaySetup();
                    SetupAnchors();
                }

                if (FilterLowVolDays && smaVolume != null && smaVolume[0] > 0 && Volume[0] < smaVolume[0] * 0.5)
                {
                    if (DebugMode && logManager != null) logManager.Log($"[Info] Skipping Low Volume Day: Volume={Volume[0]:F2}, SMA={smaVolume[0]:F2}", "Info");
                    return;
                }

                if (!ShouldContinueProcessing(currentTimeLocal)) return;

                if (CurrentBar >= 5 && avwap != null && !double.IsNaN(avwap[0]) && CurrentBar % 5 == 0)
                {
                    try
                    {
                        int lookback = Math.Min(5, CurrentBar);
                        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
                        int validPointCount = 0;

                        for (int i = 0; i < lookback; i++)
                        {
                            if (i > CurrentBar || double.IsNaN(avwap[i])) continue;
                            double x = i;
                            double y = avwap[i];
                            sumX += x;
                            sumY += y;
                            sumXY += x * y;
                            sumXX += x * x;
                            validPointCount++;
                        }

                        if (validPointCount >= 2)
                        {
                            double n = validPointCount;
                            double denominator = (n * sumXX - sumX * sumX);
                            avwapSlope[0] = denominator != 0 ? (n * sumXY - sumX * sumY) / denominator : 0;
                        }
                        else
                        {
                            avwapSlope[0] = 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        avwapSlope[0] = 0;
                        if (DebugMode && logManager != null) logManager.Log($"[Error] Slope calculation error: {ex.Message}", "Error");
                    }
                }
                else if (CurrentBar < 5 || avwap == null)
                {
                    avwapSlope[0] = 0;
                }

                if (DebugMode && CurrentBar % 10 == 0 && avwap != null && !double.IsNaN(avwap[0]))
                    logManager?.Log($"[Trace] Bar:{CurrentBar}, Time:{Time[0]:HH:mm:ss}, Close:{Close[0]:F2}, AVWAP:{avwap[0]:F2}", "Debug");

                if (DebugMode && CurrentBar % 10 == 0 && avwapSlope != null && !double.IsNaN(avwapSlope[0]))
                    logManager?.Log($"[Trace] Bar:{CurrentBar}, Slope:{avwapSlope[0]:F4}", "Debug");

                // TimeframeManager update removed

                if (UseDynamicMode && CurrentBar >= 20)
                {
                    if (Time[0].Minute % DynamicUpdateIntervalMinutes == 0)
                    {
                        AnchorTypeEnum newAnchor = DetermineAnchorType();
                        if (avwap != null && avwap.AnchorTypeSelection != newAnchor && Position.MarketPosition == MarketPosition.Flat)
                        {
                            AnchorTypeSelection = newAnchor;
                            SetAvwapAnchorType(avwap, newAnchor);
                            if (DebugMode && logManager != null) logManager.Log($"[Dynamic] Anchor switched to: {AnchorTypeSelection.ToString()} @ {Time[0]:HH:mm:ss}", "Debug");
                        }

                        EvaluateMarketConditionsAndSetEntryMethod();
                    }

                    if (DebugMode && avwap != null)
                    {
                        if (State == State.Realtime)
                        {
                            Draw.Text(this, "Decision_" + CurrentBar, $"A:{AnchorTypeSelection.ToString()} | E:{EntryMethodSelection.ToString()}", 0, High[0] + TickSize * 10, Brushes.Gray);
                            if (sessionEndTime > DateTime.MinValue)
                            {
                                TimeSpan remaining = sessionEndTime - currentTimeLocal;
                                if (remaining.TotalMinutes > 0)
                                    Draw.Text(this, "SessionClock_" + CurrentBar, $"⏳ {remaining:hh\\:mm}", 0, Low[0] - TickSize * 10, Brushes.DimGray);
                            }
                        }
                    }
                }

                if (avwap != null && (AnchorTypeSelection == NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar || AnchorTypeSelection == NinjaTrader.NinjaScript.AnchorTypeEnum.GapOpening) && !anchorLogged && !double.IsNaN(avwap[0]))
                {
                    logManager?.Log($"[Info] AVWAP Anchor Set: Time={currentTimeLocal:HH:mm:ss}, Value={avwap[0]:F2}", "Info");
                    anchorLogged = true;
                }

                if (avwap != null)
                {
                    if (EnablePinchDetection && !isPinchFormed)
                        CheckForPinchFormation(Close[0]);

                    if (EnableHandoffDetection && !handoffDetected)
                        CheckForHandoffPattern(Close[0]);

                    if (handoffDetected)
                    {
                        logManager?.LogTradeEvent("Handoff", handoffPrice, 0, $"Re-anchoring main AVWAP to handoff point: Long/Short at Bar {lastHandoffBar}");
                        avwap.AnchorTypeSelection = AnchorTypeEnum.Custom;
                        avwap.AnchorStartTime = handoffTime;
                        avwap.Reset();

                        handoffDetected = false;
                        waitingForHandoffEntry = false;
                        lastHandoffBar = CurrentBar;
                    }

                    if (Position.MarketPosition != MarketPosition.Flat)
                    {
                        // ✅ DIFF 30: Prevent exit logic from firing on entry bar or the one after
                        if (entryBarIndex >= 0 && CurrentBar <= entryBarIndex + 1)
                        {
                            if (DebugMode && logManager != null)
                                logManager?.Log($"[ENTRY_BAR_GUARD] Blocking exit logic - CurrentBar:{CurrentBar}, EntryBar:{entryBarIndex}", "Debug");
                            return;
                        }

                        if (DebugMode && logManager != null)
                        {
                            if (lastPosition != Position.MarketPosition)
                            {
                                logManager?.Log($"[Debug][InPositionCheck] Bar:{CurrentBar} Position Changed:{Position.MarketPosition} (was {lastPosition})", "Debug");
                                lastPosition = Position.MarketPosition;
                            }
                        }

                        double currentUnrealized = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency);
                        if (statsTracker != null) statsTracker.UpdateEquity(currentUnrealized);

                        // Catastrophic PnL stop logic moved to BIP 1

                        // ✅ CRITICAL FIX: Check at 100% of MaxRiskPerTrade, not 90%
                        double dailyRiskThreshold = -MaxRiskPerTrade;
                        if (DebugMode && logManager != null && (currentUnrealized <= dailyRiskThreshold || Math.Abs(currentUnrealized - dailyRiskThreshold) < MaxRiskPerTrade * 0.1))
                        {
                            logManager?.Log($"[Debug][RiskCheck] Bar:{CurrentBar} Unrealized:{currentUnrealized:F2} MaxRiskThreshold:{dailyRiskThreshold:F2}", "Debug");
                        }

                        if (currentUnrealized <= dailyRiskThreshold)
                        {
                            if (!dailyStopHit)
                            {
                                logManager?.LogTradeEvent("Critical", Close[0], Position.Quantity, $"CRITICAL: Max Risk Per Trade hit! Unrealized PnL: {currentUnrealized:F2}");
                                dailyStopHit = true;
                                SubmitExit("MaxRiskPerTrade_Hit");
                                lastPosition = MarketPosition.Flat;
                                Print($"[CRITICAL_RISK_BREACH] MaxRiskPerTrade exceeded: {currentUnrealized:F2} <= {dailyRiskThreshold:F2}");
                                return;
                            }
                        }

                        double totalDailyPnl = (statsTracker?.DailyPnL ?? 0) + currentUnrealized;

                        if (totalDailyPnl <= -MaxDailyLoss)
                        {
                            if (!dailyStopHit)
                            {
                                logManager?.LogTradeEvent("Critical", Close[0], Position.Quantity, $"Max Daily Loss hit intra-trade! Total PnL: {totalDailyPnl:F2}");
                                dailyStopHit = true;
                                SubmitExit("MaxDailyLoss_Hit");
                                lastPosition = MarketPosition.Flat;
                                return;
                            }
                        }

                        // ✅ DIFF 74: Session end detection only - NO immediate exit, NO return
                        if (ToTime(Time[0]) >= 155900)
                        {
                            Print($"[SESSION_END_DETECTED] Time={Time[0]} | Position={Position.MarketPosition} | TrailArmed={mfeTrailArmed}");
                            sessionCloseTriggered = true;
                            // NO return - let trail logic handle exits
                        }



                        // Verify target hit before AVWAP violation - Moved to 1-min check
                        // VerifyTargetHit(Close[0]); // Original call removed

                        // AVWAP violation exit removed - only using MFE trail for exits
                    }
                    else
                    {
                        if (lastPosition != MarketPosition.Flat && DebugMode && logManager != null)
                        {
                            logManager?.Log($"[Debug][InPositionCheck] Bar:{CurrentBar} Position Changed: Flat (was {lastPosition})", "Debug");
                            lastPosition = MarketPosition.Flat;
                        }

                        if (statsTracker != null) statsTracker.UpdateEquity(0.0);

                        if (sessionStartTime > DateTime.MinValue && currentTimeLocal >= sessionStartTime.AddMinutes(AnchorTypeSelection == NinjaTrader.NinjaScript.AnchorTypeEnum.OpeningRange ? 30 : 0))
                        {
                            switch (EntryMethodSelection)
                            {
                                case EntryMethod.Touch:
                                case EntryMethod.Momentum:
                                    CheckForStandardEntry(Close[0], Time[0]);
                                    break;
                                case EntryMethod.SecondDay:
                                    CheckForSecondDayEntry(Close[0], Time[0]);
                                    break;
                                case EntryMethod.PinchBreakout:
                                    CheckForPinchBreakout(Close[0], Time[0]);
                                    break;
                                case EntryMethod.GreenToRed:
                                    CheckForGreenToRedEntry(Close[0], Time[0]);
                                    break;
                                case EntryMethod.RedToGreen:
                                    CheckForRedToGreenEntry(Close[0], Time[0]);
                                    break;
                                case EntryMethod.Handoff:
                                    CheckForHandoffEntry(Close[0], Time[0]);
                                    break;
                            }
                        }
                    }
                }
            }
            // --- MFE Trail Reset Catch for Flat Positions ---
            if (Position.MarketPosition == MarketPosition.Flat && (mfeTrailArmed || tradeManager?.exitLogged == true || mfeTrailMaxPnL > 0 || breakevenStopActive || atrTrailActive))
            {
                mfeTrailArmed = false;
                mfeTrailArmedTime = Core.Globals.MinDate;
                mfeTrailArmedPrice = 0;
                mfeTrailMaxPnL = 0;
                initialStopPrice = 0.0;
                breakevenPrice = 0.0;
                trailingStopPrice = 0.0;
                breakevenStopActive = false;
                atrTrailActive = false;
                // ✅ DIFF 43: Removed obsolete mfeExitSubmitted reset
                currentTradeID = ""; // Reset current trade ID when position is flat
                exitSubmitted = false; // Reset exit flag when position is flat

                // ✅ DIFF 30: Reset entry bar guard when position is flat
                entryBarIndex1Min = -1;
                entryBarIndex = -1;

                // ✅ DIFF 31: Reset entry price when position is flat
                entryPrice = 0.0;

                // ✅ DIFF 41: Reset cached entry data when position is flat
                cachedEntryPrice = 0.0;
                cachedEntryTime = DateTime.MinValue;
                isTradeActive = false;
                entryCapturedFallback = false;

                // ✅ DIFF 44: Reset trail logging enforcement flag
                mfeTrailShouldHaveLogged = false;

                // Note: stupidStopDist stays constant - it's our "let it run" distance

                Print($"[AutoReset] MFE Trail state cleared due to flat position @ {Time[0]}");
            }

            if (!ShouldContinueProcessing(Time[0]))
                return;

            if (BarsInProgress == 1)
            {
                if (Position.MarketPosition == MarketPosition.Flat || tradeManager == null || exitLogged)
                {
                    return;
                }

                // Store the 1-min entry bar index if not yet recorded
                if (entryBarIndex1Min == -1 && entryBarIndex != -1)
                {
                    entryBarIndex1Min = CurrentBars[1];
                }

                // ✅ DIFF 78: REMOVED ENTRY GUARD - This was blocking trail arming for trades with quick MFE peaks
                // The entry guard was preventing trail arming for trades #44 and #16 where MFE peaked quickly
                // Print($"[MFE Trail Entry Guard REMOVED] CurrentBars[1]={CurrentBars[1]} | entryBarIndex1Min={entryBarIndex1Min}");

                // ✅ DIFF 42: Fallback entry capture in case OnExecutionUpdate fails
                if (!isTradeActive && Position.MarketPosition != MarketPosition.Flat && cachedEntryPrice == 0.0)
                {
                    cachedEntryPrice = Position.AveragePrice;
                    cachedEntryTime = Time[0];
                    isTradeActive = true;
                    entryCapturedFallback = true;
                    Print($"[FallbackEntryCapture] No execution update. Fallback entryPrice={cachedEntryPrice:F2} at {cachedEntryTime}");
                }

                // --- [MFE Tick Debug] Trail Logic Diagnostic ---
                if (EnableMFETrail && Position.MarketPosition != MarketPosition.Flat && tradeManager != null)
                {
                    string trailDebugID = currentTradeID ?? "<no_trade_id>";
                    double curPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                    double maxPnL = tradeManager.MaxFavorableExcursion;
                    bool armed = mfeTrailArmed;

                    // ✅ DIFF 77: Moved arming logic to BIP 1 to fix timing issue

                    Print($"[MFE Tick Debug FOCUSED_TEST] ID={trailDebugID} | curPnL={curPnL:F2} | MaxPnL={maxPnL:F2} | Armed={armed} | ExitLogged={exitLogged} | ArmIf>=${MfeTrailArmThreshold:F0} | ExitAt={mfeTrailExitThreshold:F2} | TestArm400_Capture65=True");

                    // ✅ DIFF 77: Enhanced diagnostic logging for TIMING-FIXED unified controller
                    if (mfeTrailArmed)
                    {
                        double trailStopPnL = frozenMFEOnArm * mfeTrailExitThreshold;
                        Print($"[UNIFIED_CONTROLLER_GUARD_REMOVED] Armed=True | FrozenMFE={frozenMFEOnArm:F2} | TrailStopPnL={trailStopPnL:F2} | CurPnL={curPnL:F2} | WillTrigger={curPnL <= trailStopPnL} | MaxMFE={tradeManager.MaxFavorableExcursion:F2}");
                    }
                    else if (tradeManager.MaxFavorableExcursion >= MfeTrailArmThreshold)
                    {
                        Print($"[MFE_ARM_READY_LOWERED] MaxMFE={tradeManager.MaxFavorableExcursion:F2} >= Threshold={MfeTrailArmThreshold:F2} | ShouldArm=True | BIP={BarsInProgress}");
                    }

                    // ✅ DIFF 75: Removed duplicate diagnostic logging
                }

                // ✅ DIFF 76: Bulletproof MFE tracking with NT8 fallback
                if (BarsInProgress == 1 && Position.MarketPosition != MarketPosition.Flat && tradeManager != null)
                {
                    // ✅ DIFF 76: Ensure we have a valid entry price for MFE calculation
                    double entryPriceForMFE = cachedEntryPrice;
                    if (entryPriceForMFE <= 0)
                    {
                        entryPriceForMFE = Position.AveragePrice;
                        Print($"[MFE_FALLBACK] Using Position.AveragePrice={entryPriceForMFE:F2} because cachedEntryPrice={cachedEntryPrice:F2}");
                    }

                    // ✅ DIFF 76: Calculate MFE using reliable entry price
                    double currentUnrealized = Position.MarketPosition == MarketPosition.Long
                        ? (Highs[1][0] - entryPriceForMFE) * Position.Quantity * Instrument.MasterInstrument.PointValue
                        : (entryPriceForMFE - Lows[1][0]) * Position.Quantity * Instrument.MasterInstrument.PointValue;

                    // ✅ DIFF 76: Also use NT8's native unrealized PnL as additional validation
                    double nt8UnrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                    double maxUnrealized = Math.Max(currentUnrealized, nt8UnrealizedPnL);

                    tradeManager.MaxFavorableExcursion = Math.Max(tradeManager.MaxFavorableExcursion, maxUnrealized);

                    // ✅ DIFF 76: Update trade map for logging consistency
                    if (!string.IsNullOrEmpty(currentTradeID))
                    {
                        tradeIdToMaxPnLMap[currentTradeID] = tradeManager.MaxFavorableExcursion;
                    }

                    // ✅ DIFF 76: Enhanced diagnostic logging
                    if (currentUnrealized != nt8UnrealizedPnL)
                    {
                        Print($"[MFE_VALIDATION] Custom={currentUnrealized:F2} | NT8={nt8UnrealizedPnL:F2} | Using={maxUnrealized:F2} | EntryPrice={entryPriceForMFE:F2}");
                    }

                    // ✅ DIFF 77: FIXED ARMING LOGIC - Now in BIP 1 AFTER MFE updates
                    if (!mfeTrailArmed && tradeManager.MaxFavorableExcursion >= MfeTrailArmThreshold)
                    {
                        mfeTrailArmed = true;
                        frozenMFEOnArm = tradeManager.MaxFavorableExcursion; // ✅ DIFF 81: Start tracking peak MFE from arming point
                        Print($"[MFE Trail Armed MAJOR_OPTIMIZATION] ID={currentTradeID} | StartingMFE={tradeManager.MaxFavorableExcursion:F2} | ArmThreshold=${MfeTrailArmThreshold:F0} | PnLSafetyNet={mfeTrailExitThreshold:F2} | ATRPrimary={MFEPeakTrailATR:F1} | Target9637Recovery=True | Bar={CurrentBars[1]} | EntryBar={entryBarIndex1Min}");

                        // ✅ DIFF 68: Cancel native stop loss to prevent conflict with manual trail exit
                        if (nativeStopLoss != null && nativeStopLoss.OrderState == OrderState.Working)
                        {
                            CancelOrder(nativeStopLoss);
                            Print($"[CancelNativeStopOnTrailArm] ID={currentTradeID} | MFE Trail Armed | Native stop order canceled: {nativeStopLoss.Name}");
                            nativeStopLoss = null;
                        }
                        else
                        {
                            Print($"[CancelNativeStopOnTrailArm] ID={currentTradeID} | No native stop to cancel (null or not working)");
                        }
                    }

                    Print($"[TRACE] Entry={cachedEntryPrice:F2} | Pos={Position.MarketPosition} | CurPnL={currentUnrealized:F2} | MaxPnL={tradeManager.MaxFavorableExcursion:F2} | TrailArmed={mfeTrailArmed} | ExitLogged={exitLogged} | TrailPx={mfeTrailStopPrice:F2} | BIP={BarsInProgress} | Time={Times[1][0]:yyyy-MM-dd HH:mm}");

                    // 🌀 Dynamic Trail Price Update AFTER Arming
                    if (mfeTrailArmed)
                    {
                        double newTrailPx = Position.MarketPosition == MarketPosition.Long
                            ? Highs[1][0] - MfeTrailStopAtrMultiplier * atr1min[0]
                            : Lows[1][0] + MfeTrailStopAtrMultiplier * atr1min[0];

                        if (Math.Abs(mfeTrailStopPrice - newTrailPx) > TickSize)
                        {
                            Print($"[MFE Trail] 🔁 Updated TrailPx from {mfeTrailStopPrice:F2} → {newTrailPx:F2} | Time={Times[1][0]:yyyy-MM-dd HH:mm}");
                            mfeTrailStopPrice = newTrailPx;
                        }
                    }

                    // ✅ DIFF 81 – FIXED MFE Trail Logic: Track peak MFE and use correct trail trigger
                    if (mfeTrailArmed && !exitLogged && !trailExitSubmitted && Position.MarketPosition != MarketPosition.Flat)
                    {
                        double unrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);

                        // Update peak MFE if current PnL is higher
                        if (unrealizedPnL > frozenMFEOnArm)
                        {
                            frozenMFEOnArm = unrealizedPnL;
                            Print($"[MFE Peak Updated] ID={currentTradeID} | New Peak MFE={frozenMFEOnArm:F2}");
                        }

                        double trailTrigger = frozenMFEOnArm * mfeTrailExitThreshold; // 85% of PEAK MFE

                        // ✅ FIX: Check BOTH PnL trail AND ATR price trail
                        bool pnlTrailHit = unrealizedPnL <= trailTrigger;
                        bool atrTrailHit = false;

                        // Check if price hit the ATR trail stop
                        if (mfeTrailStopPrice > 0)
                        {
                            if (Position.MarketPosition == MarketPosition.Long)
                                atrTrailHit = Low[0] <= mfeTrailStopPrice;
                            else if (Position.MarketPosition == MarketPosition.Short)
                                atrTrailHit = High[0] >= mfeTrailStopPrice;
                        }

                        Print($"[MFE Trail Check HYBRID_OPTIMIZED] ID={currentTradeID} | unrealizedPnL={unrealizedPnL:F2} | pnlTrigger={trailTrigger:F2} | atrTrailPx={mfeTrailStopPrice:F2} | peakMFE={frozenMFEOnArm:F2} | pnlHit={pnlTrailHit} | atrHit={atrTrailHit} | pnlCapture={mfeTrailExitThreshold:F2}");

                        if (pnlTrailHit || atrTrailHit)
                        {
                            // ✅ FIX: Specific exit reason based on which trail triggered
                            string exitReason = atrTrailHit ? "ATR_Price_Trail_Exit" : "PnL_Percentage_Trail_Exit";

                            logManager?.Log($"[MFE_EXIT_TRIGGERED_FIXED] curPnL={unrealizedPnL:F2} | trailTrigger={trailTrigger:F2} | peakMFE={frozenMFEOnArm:F2} | captureRate={unrealizedPnL/frozenMFEOnArm:F2} | exitType={exitReason}", "Info");
                            Print($"[TRAIL EXIT TRIGGERED FIXED] ID={currentTradeID} | Reason={exitReason} | PnL={unrealizedPnL:F2} | PeakMFE={frozenMFEOnArm:F2} | Capture={unrealizedPnL/frozenMFEOnArm:P1} | ATRTrailPx={mfeTrailStopPrice:F2}");

                            // ✅ DIFF 75: Log FIRST while state is intact
                            WriteMFETrailLogOnce(
                                currentTradeID,
                                currentSignalName,
                                cachedEntryPrice,
                                Close[0],
                                cachedEntryTime,
                                Time[0],
                                tradeManager.MaxFavorableExcursion,
                                unrealizedPnL,
                                exitReason,
                                false,
                                frozenMFEOnArm,
                                unrealizedPnL);

                            // ✅ DIFF 75: Submit exit using unified controller
                            SubmitExit("MFE_ATR_Trail_Exit_IntraBar");

                            // ✅ DIFF 75: Reset state AFTER exit submission
                            mfeTrailArmed = false;
                            frozenMFEOnArm = 0.0;
                            currentTradeID = "";
                        }

                        // ✅ DIFF 75: Removed conflicting trail exit enforcer - unified controller handles all exits

                    // ✅ CRITICAL FIX: Emergency intra-bar stop loss protection
                    if (Position.MarketPosition != MarketPosition.Flat && !exitLogged && tradeManager != null)
                    {
                        double entryPrice = tradeManager.EntryPrice;
                        double stopDistance = currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance();

                        bool emergencyStopHit = false;
                        string emergencyReason = "";

                        if (Position.MarketPosition == MarketPosition.Long)
                        {
                            double emergencyStopPrice = entryPrice - stopDistance;
                            if (Low[0] <= emergencyStopPrice)
                            {
                                emergencyStopHit = true;
                                emergencyReason = "Emergency_Long_Stop_Hit";
                                Print($"[EMERGENCY_STOP] Long position hit stop: Low[0]={Low[0]:F2} <= StopPrice={emergencyStopPrice:F2}");
                            }
                        }
                        else if (Position.MarketPosition == MarketPosition.Short)
                        {
                            double emergencyStopPrice = entryPrice + stopDistance;
                            if (High[0] >= emergencyStopPrice)
                            {
                                emergencyStopHit = true;
                                emergencyReason = "Emergency_Short_Stop_Hit";
                                Print($"[EMERGENCY_STOP] Short position hit stop: High[0]={High[0]:F2} >= StopPrice={emergencyStopPrice:F2}");
                            }
                        }

                        if (emergencyStopHit)
                        {
                            double currentUnrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);
                            Print($"[EMERGENCY_STOP_TRIGGERED] ID={currentTradeID} | Reason={emergencyReason} | PnL={currentUnrealizedPnL:F2}");

                            // Log emergency stop
                            WriteMFETrailLogOnce(
                                currentTradeID,
                                currentSignalName,
                                cachedEntryPrice,
                                Close[0],
                                cachedEntryTime,
                                Time[0],
                                tradeManager.MaxFavorableExcursion,
                                currentUnrealizedPnL,
                                emergencyReason,
                                true, // wasOverride = true
                                frozenMFEOnArm,
                                currentUnrealizedPnL);

                            SubmitExit(emergencyReason);

                            // Reset state after exit
                            mfeTrailArmed = false;
                            frozenMFEOnArm = 0.0;
                            currentTradeID = "";
                            return;
                        }
                    }

                    // ✅ DIFF 83 – EMERGENCY 3:45 PM EXIT: Prevent SessionEnd disasters
                    if (ToTime(Times[1][0]) >= 154500 && Position.MarketPosition != MarketPosition.Flat && !exitLogged)
                    {
                        double currentUnrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);

                        Print($"[EMERGENCY_345PM_EXIT] ID={currentTradeID} | Time={Times[1][0]} | PnL={currentUnrealizedPnL:F2} | Preventing SessionEnd disaster");

                        // Log as emergency exit
                        WriteMFETrailLogOnce(
                            currentTradeID,
                            currentSignalName,
                            cachedEntryPrice,
                            Close[0],
                            cachedEntryTime,
                            Time[0],
                            tradeManager.MaxFavorableExcursion,
                            currentUnrealizedPnL,
                            "Emergency_345PM_Exit",
                            true, // wasOverride = true
                            frozenMFEOnArm,
                            currentUnrealizedPnL);

                        // ✅ Use unified exit controller
                        SubmitExit("Emergency_345PM_Exit");

                        // Reset state after exit
                        mfeTrailArmed = false;
                        frozenMFEOnArm = 0.0;
                        currentTradeID = "";

                        Print($"[EMERGENCY_345PM_EXIT] ✅ Emergency exit executed | PnL={currentUnrealizedPnL:F2}");
                    }

                    // ✅ DIFF 74 – EOD FAILSAFE: Force exit armed trails at session end (backup)
                    else if (ToTime(Times[1][0]) >= 155900 && Position.MarketPosition != MarketPosition.Flat && mfeTrailArmed && !exitLogged)
                    {
                        double currentUnrealizedPnL = Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency, Close[0]);

                        Print($"[TRAIL_FORCED_EOD] ID={currentTradeID} | Time={Times[1][0]} | Armed={mfeTrailArmed} | ExitLogged={exitLogged} | MaxMFE={frozenMFEOnArm:F2}");

                        // ✅ DIFF 75: Log FIRST while state is intact
                        WriteMFETrailLogOnce(
                            currentTradeID,
                            currentSignalName,
                            cachedEntryPrice,
                            Close[0],
                            cachedEntryTime,
                            Time[0],
                            tradeManager.MaxFavorableExcursion,
                            currentUnrealizedPnL,
                            "Trail_Forced_EOD",
                            true, // wasOverride = true
                            frozenMFEOnArm,
                            currentUnrealizedPnL);

                        // ✅ Use unified exit controller
                        SubmitExit("Trail_Forced_EOD");

                        Print($"[TRAIL_FORCED_EOD] ✅ Forced EOD exit | ID={currentTradeID} | Reason=Trail_Forced_EOD | PnL={currentUnrealizedPnL:F2}");

                        // Reset state after exit
                        mfeTrailArmed = false;
                        frozenMFEOnArm = 0.0;
                        currentTradeID = "";
                    }

                        // ✅ DIFF 59: Warning logger for high MFE trades that didn't arm trail
                        if (!mfeTrailArmed && tradeManager.MaxFavorableExcursion >= MfeTrailArmThreshold && Position.MarketPosition != MarketPosition.Flat)
                        {
                            int barsHeld = tradeManager != null && tradeManager.EntryBar >= 0 ? (CurrentBars[1] - tradeManager.EntryBar) : 0;
                            logManager?.Log(string.Format("[WARNING] High MFE but Trail NOT ARMED | TradeID={0} | MaxPnL={1:C} | ArmingThreshold={2:C} | BarsHeld={3}",
                                currentTradeID,
                                tradeManager.MaxFavorableExcursion,
                                MfeTrailArmThreshold,
                                barsHeld.ToString()), "Warning");
                        }


                    }
                }
                else
                {
                    // ✅ DIFF 51: Enhanced MFE Trail Blocker Logging
                    Print($"[MFE Trail Blocked] enable={EnableMFETrail} | pos={Position.MarketPosition} | exitLogged={exitLogged} | tradeMgr={(tradeManager != null)} | currentID={currentTradeID} | time={Times[1][0]}");
                }
            }
        }

        private void CheckForStandardEntry(double price, DateTime time)
        {
            if (dailyStopHit || double.IsNaN(avwap[0])) return;

            double atrValue = atr14[0];
            double adxValue = adx14[0];
            double volumeRatio = GetVolumeRatio();
            double avwapValue = avwap[0];
            double slope = avwapSlope[0];

            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            bool trendConfirmed = adxValue > MinADXForEntry;
            bool volatilityOk = atrValue >= 0.1 && atrValue <= 50.0;
            bool timeframeAligned = true; // Simplified

            if (DebugMode && CurrentBar % 5 == 0)
                logManager?.Log($"[Debug][CheckStdEntry] Bar:{CurrentBar} Filters - Vol:{volumeConfirmed}, Trend:{trendConfirmed}, Volatility:{volatilityOk}, Align:{timeframeAligned}", "Debug");
            if (DebugMode && (!volumeConfirmed || !trendConfirmed || !volatilityOk || !timeframeAligned))
                logManager?.LogDebug("Debug", "Filter", 0, 0, $"Entry Rejected: Vol={volumeConfirmed}, Trend={trendConfirmed}, Volat={volatilityOk}, Align={timeframeAligned}");

            // --- Momentum Logic with Bar Confirmation ---
            if (timeframeAligned && EntryMethodSelection == EntryMethod.Momentum &&
                price > avwapValue && slope > 0 && // Price crossed above rising AVWAP
                Math.Abs(slope) <= MaxAVWAPSlope &&
                volumeConfirmed && trendConfirmed && volatilityOk &&
                Close[0] > Open[0] && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat) // <<< Added: Confirmation Bar must be bullish
            {
                EnterLongTrade(price, time, "Momentum_Confirmed");
                entrySubmitted = true;
            }
            else if (timeframeAligned && EntryMethodSelection == EntryMethod.Momentum &&
                     price < avwapValue && slope < 0 && // Price crossed below falling AVWAP
                     Math.Abs(slope) <= MaxAVWAPSlope &&
                     volumeConfirmed && trendConfirmed && volatilityOk &&
                     Close[0] < Open[0] && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat) // <<< Added: Confirmation Bar must be bearish
            {
                EnterShortTrade(price, time, "Momentum_Confirmed");
                entrySubmitted = true;
            }
            // --- Touch Logic (remains the same, but won't be called if Momentum is selected) ---
            else if (timeframeAligned && EntryMethodSelection == EntryMethod.Touch && CurrentBar > 1 &&
                     Close[1] < avwap[1] && price >= avwapValue && slope > 0 &&
                     Math.Abs(slope) <= MaxAVWAPSlope && volumeConfirmed && trendConfirmed && volatilityOk && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLongTrade(price, time, "Touch");
                entrySubmitted = true;
            }
            else if (timeframeAligned && EntryMethodSelection == EntryMethod.Touch && CurrentBar > 1 &&
                     Close[1] > avwap[1] && price <= avwapValue && slope < 0 &&
                     Math.Abs(slope) <= MaxAVWAPSlope && volumeConfirmed && trendConfirmed && volatilityOk && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShortTrade(price, time, "Touch");
                entrySubmitted = true;
            }
        }

        private void CheckForSecondDayEntry(double price, DateTime time)
        {
            if (dailyStopHit || !isSecondDayOfMove || avwap == null || double.IsNaN(avwap[0]) || avwapPriorDay == null || double.IsNaN(avwapPriorDay[0])) return;

            double currentDayVWAP = avwap[0];
            double priorDayAVWAP = avwapPriorDay[0];

            double atrValue = atr14[0];
            double volumeRatio = GetVolumeRatio();
            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            // Simplified without TimeframeManager
            bool timeframeAligned = true;
            double testTolerance = atrValue * 0.25;

            bool testedPriorDayAVWAP_Low = Low[1] <= priorDayAVWAP + testTolerance || (CurrentBar > 2 && Low[2] <= avwapPriorDay[1] + testTolerance);
            bool crossingCurrentDayVWAP_Up = price > currentDayVWAP && Close[1] <= avwap[1];

            bool secondDayLongSetup = timeframeAligned && testedPriorDayAVWAP_Low && crossingCurrentDayVWAP_Up && volumeConfirmed;
            if (DebugMode && secondDayLongSetup) logManager?.Log($"[Debug][SecondDayLong] Trigger Check: testedPriorLow={testedPriorDayAVWAP_Low}, crossCurrentUp={crossingCurrentDayVWAP_Up}, vol={volumeConfirmed}", "Debug");

            bool testedPriorDayAVWAP_High = High[1] >= priorDayAVWAP - testTolerance || (CurrentBar > 2 && High[2] >= avwapPriorDay[1] - testTolerance);
            bool crossingCurrentDayVWAP_Down = price < currentDayVWAP && Close[1] >= avwap[1];

            bool secondDayShortSetup = timeframeAligned && testedPriorDayAVWAP_High && crossingCurrentDayVWAP_Down && volumeConfirmed;
            if (DebugMode && secondDayShortSetup) logManager?.Log($"[Debug][SecondDayShort] Trigger Check: testedPriorHigh={testedPriorDayAVWAP_High}, crossCurrentDown={crossingCurrentDayVWAP_Down}, vol={volumeConfirmed}", "Debug");

            if (secondDayLongSetup && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLongTrade(price, time, "SecondDay");
                entrySubmitted = true;
            }
            else if (secondDayShortSetup && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShortTrade(price, time, "SecondDay");
                entrySubmitted = true;
            }
        }

        private void CheckForPinchBreakout(double price, DateTime time)
        {
            if (dailyStopHit || !isPinchFormed || double.IsNaN(avwap[0]) || double.IsNaN(avwapPriorHigh[0]) || double.IsNaN(avwapPriorLow[0]) ||
                (time - pinchFormationTime).TotalMinutes < MinTimeAfterPinchMinutes) return;

            double atrValue = atr14[0];
            double volumeRatio = GetVolumeRatio();
            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            // Simplified without TimeframeManager
            bool timeframeAligned = true;

            bool longBreakout = timeframeAligned && CurrentBar > 1 &&
                                price > avwapPriorHigh[0] && Close[1] <= avwapPriorHigh[0] &&
                                volumeConfirmed;

            bool shortBreakout = timeframeAligned && CurrentBar > 1 &&
                                 price < avwapPriorLow[0] && Close[1] >= avwapPriorLow[0] &&
                                 volumeConfirmed;

            if (longBreakout && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLongTrade(price, time, "PinchBreakout");
                entrySubmitted = true;
            }
            else if (shortBreakout && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShortTrade(price, time, "PinchBreakout");
                entrySubmitted = true;
            }
        }

        private void CheckForGreenToRedEntry(double price, DateTime time)
        {
            if (dailyStopHit || double.IsNaN(avwap[0])) return;

            double atrValue = atr14[0];
            double volumeRatio = GetVolumeRatio();
            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            // Simplified without TimeframeManager
            bool timeframeAligned = true;

            bool greenToRedSetup = timeframeAligned && CurrentBar > 1 &&
                                   Open[0] > avwap[0] && price < avwap[0] &&
                                   Close[1] > avwap[1] && volumeConfirmed;

            if (greenToRedSetup && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShortTrade(price, time, "GreenToRed");
                entrySubmitted = true;
            }
        }

        private void CheckForRedToGreenEntry(double price, DateTime time)
        {
            if (dailyStopHit || double.IsNaN(avwap[0])) return;

            double atrValue = atr14[0];
            double volumeRatio = GetVolumeRatio();
            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            // Simplified without TimeframeManager
            bool timeframeAligned = true;

            bool redToGreenSetup = timeframeAligned && CurrentBar > 1 &&
                                   Open[0] < avwap[0] && price > avwap[0] &&
                                   Close[1] < avwap[1] && volumeConfirmed;

            if (redToGreenSetup && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLongTrade(price, time, "RedToGreen");
                entrySubmitted = true;
            }
        }

        private void CheckForHandoffEntry(double price, DateTime time)
        {
            if (dailyStopHit || !handoffDetected || double.IsNaN(avwap[0]) || !waitingForHandoffEntry) return;

            double atrValue = atr14[0];
            double volumeRatio = GetVolumeRatio();
            bool volumeConfirmed = volumeRatio >= MinVolRatio;
            // Simplified without TimeframeManager
            bool timeframeAligned = true;

            bool longHandoff = timeframeAligned && CurrentBar > 1 &&
                               price > avwap[0] && price > handoffPrice && (price - handoffPrice) > (atr14[0] * 0.25) &&
                               Close[1] <= avwap[1] && volumeConfirmed;

            bool shortHandoff = timeframeAligned && CurrentBar > 1 &&
                                price < avwap[0] && price < handoffPrice && (handoffPrice - price) > (atr14[0] * 0.25) &&
                                Close[1] >= avwap[1] && volumeConfirmed;

            if (longHandoff && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLongTrade(price, time, "Handoff");
                waitingForHandoffEntry = false;
                entrySubmitted = true;
            }
            else if (shortHandoff && !entrySubmitted && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShortTrade(price, time, "Handoff");
                waitingForHandoffEntry = false;
                entrySubmitted = true;
            }
        }

        private void EnterLongTrade(double price, DateTime time, string signalName)
        {
            int positionSize = tradeManager.CalculatePositionSize(price);
            double stopPrice = CalculateLongStopPrice(price);
            double targetPrice = price + (atr14[0] * ProfitTargetATR);
            // Ensure 1-minute series is ready for fill checks
            if (BarsArray[1] == null || CurrentBars[1] < BarsRequiredToTrade)
            {
                logManager?.Log($"[Error] EnterLongTrade: 1-minute series not ready at Bar:{CurrentBar}", "Error");
                return;
            }
            signalName = $"{signalName}_Long_{++dailyTradeCount}";

            // PATCH A: Assign currentSignalName to support native stop cancellation
            this.currentSignalName = signalName;

            // Reset exitOrdersPlaced flag for new trade
            exitOrdersPlaced = false;
            // Set intended entry size and reset filled quantity for partial fill tracking
            intendedEntrySize = positionSize;
            totalFilledQuantity = 0;
            if (DebugMode && logManager != null)
            {
                logManager.Log($"[Debug][EnterLong] exitOrdersPlaced reset to false for {signalName}", "Debug");
                logManager.Log($"[Debug][EnterLong] Intended entry size set to {intendedEntrySize} for {signalName}", "Debug");
            }

            if (DebugMode && logManager != null)
            {
                double atrValue = atr14[0];
                double stopDistance = atrValue * StopDistanceATR;
                double riskPerContract = stopDistance * instrumentPointValue;
                // logManager?.Log($"[Debug][CalcPosSize] ATR:{atrValue:F2}, StopDist:{stopDistance:F2}, Risk/Contr:{riskPerContract:F2}, MaxRisk:{MaxRiskPerTrade:F2} => RawSize:{(int)Math.Floor(MaxRiskPerTrade / riskPerContract)}, FinalSize:{positionSize}", "Debug");
            }

            EnterLong(positionSize, signalName);
            // Removed SetTargetOrder call - target price is calculated in ManageOpenPositionWithBracket
            tradeManager.EntryPrice = price; // Store entry price
            // ✅ CRITICAL FIX: Always submit native stop for protection, cancel only when MFE trail arms
            double backsolvedStopPrice = price - (currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance());
            SetStopLoss(CalculationMode.Price, backsolvedStopPrice);
            Print($"[NATIVE_STOP_SUBMITTED] Long entry with native stop at {backsolvedStopPrice:F2} (Risk: ${Math.Abs(price - backsolvedStopPrice) * positionSize * instrumentPointValue:F0})");

            if (DebugMode && logManager != null)
                logManager?.Log($"[Debug][TargetCheck] Long Trade Entered: Signal={signalName}, TargetPrice={targetPrice:F2}, 1-minute fills enabled", "Debug");

            logManager?.LogTradeEvent("Entry", price, positionSize, "Long Entry", stopPrice, targetPrice);

            if (ExportSignals)
            {
                ExportSignal(time, signalName, price);
            }
        }

        private void EnterShortTrade(double price, DateTime time, string signalName)
        {
            int positionSize = tradeManager.CalculatePositionSize(price);
            double stopPrice = CalculateShortStopPrice(price);
            double targetPrice = price - (atr14[0] * ProfitTargetATR);
            // Ensure 1-minute series is ready for fill checks
            if (BarsArray[1] == null || CurrentBars[1] < BarsRequiredToTrade)
            {
                logManager?.Log($"[Error] EnterShortTrade: 1-minute series not ready at Bar:{CurrentBar}", "Error");
                return;
            }
            signalName = $"{signalName}_Short_{++dailyTradeCount}";

            // PATCH A: Assign currentSignalName to support native stop cancellation
            this.currentSignalName = signalName;

            // Reset exitOrdersPlaced flag for new trade
            exitOrdersPlaced = false;
            // Set intended entry size and reset filled quantity for partial fill tracking
            intendedEntrySize = positionSize;
            totalFilledQuantity = 0;
            if (DebugMode && logManager != null)
            {
                logManager.Log($"[Debug][EnterShort] exitOrdersPlaced reset to false for {signalName}", "Debug");
                logManager.Log($"[Debug][EnterShort] Intended entry size set to {intendedEntrySize} for {signalName}", "Debug");
            }

            if (DebugMode && logManager != null)
            {
                double atrValue = atr14[0];
                double stopDistance = atrValue * StopDistanceATR;
                double riskPerContract = stopDistance * instrumentPointValue;
                // logManager?.Log($"[Debug][CalcPosSize] ATR:{atrValue:F2}, StopDist:{stopDistance:F2}, Risk/Contr:{riskPerContract:F2}, MaxRisk:{MaxRiskPerTrade:F2} => RawSize:{(int)Math.Floor(MaxRiskPerTrade / riskPerContract)}, FinalSize:{positionSize}", "Debug");
            }

            EnterShort(positionSize, signalName);
            // Removed SetTargetOrder call - target price is calculated in ManageOpenPositionWithBracket
            tradeManager.EntryPrice = price; // Store entry price
            // ✅ CRITICAL FIX: Always submit native stop for protection, cancel only when MFE trail arms
            double backsolvedStopPrice = price + (currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance());
            SetStopLoss(CalculationMode.Price, backsolvedStopPrice);
            Print($"[NATIVE_STOP_SUBMITTED] Short entry with native stop at {backsolvedStopPrice:F2} (Risk: ${Math.Abs(price - backsolvedStopPrice) * positionSize * instrumentPointValue:F0})");

            if (DebugMode && logManager != null)
                logManager?.Log($"[Debug][TargetCheck] Short Trade Entered: Signal={signalName}, TargetPrice={targetPrice:F2}, 1-minute fills enabled", "Debug");

            logManager?.LogTradeEvent("Entry", price, positionSize, "Short Entry", stopPrice, targetPrice);

            if (ExportSignals)
            {
                ExportSignal(time, signalName, price);
            }
        }

        // CheckForAVWAPViolation method removed - AVWAP violation exit logic removed
        #endregion

        #region Helper Methods
        private bool ShouldContinueProcessing(DateTime currentTime)
        {
            // --- Session End Check ---
            if (currentTime >= sessionEndTime && !sessionEndExitLogged)
            {
                if (Position.MarketPosition != MarketPosition.Flat)
                {
                    SubmitExit("SessionEnd_Manual");
                    sessionEndExitLogged = true;
                }
                return false;
            }
            else if (currentTime >= sessionEndTime && sessionEndExitLogged)
            {
                return false;
            }

            // --- Daily Drawdown & PnL Checks ---
            double riskPerTrade = atr14 != null && atr14[0] > 0
                                 ? atr14[0] * StopDistanceATR * instrumentPointValue * tradeManager.CalculatePositionSize(Close[0])
                                 : MaxRiskPerTrade;
            bool pnlOk = (statsTracker.DailyPnL - riskPerTrade) > -MaxDailyLoss;
            bool dailyDrawdownOk = statsTracker.DailyMaxDrawdown <= MaxDailyLoss;

            if (!dailyDrawdownOk || !pnlOk)
            {
                if (!dailyStopHit) // Log only once per day
                {
                    dailyStopHit = true;
                    if (DebugMode)
                        logManager?.Log($"[Critical] Daily Stop Hit! Reason => DailyDD Ok: {dailyDrawdownOk} (DD:{statsTracker.DailyMaxDrawdown:F2}/{MaxDailyLoss:F2}), PnL OK: {pnlOk} (PnL:{statsTracker.DailyPnL:F2}, Risk:{riskPerTrade:F2})", "Critical");
                }

                if (Position.MarketPosition != MarketPosition.Flat)
                    SubmitExit("MaxDailyLoss_DD_Hit");

                return false; // Stop processing for the day
            }
            else if (DebugMode && CurrentBar % 10 == 0) // Log normal DD check if debug is on
            {
                // Log both daily and the new intra-trade cumulative drawdown
                logManager?.Log($"[Debug][DrawdownCheck] Bar:{CurrentBar} Daily DD:{statsTracker.DailyMaxDrawdown:F2}/{MaxDailyLoss:F2}", "Debug");
            }

            return !dailyStopHit;
        }

        private double GetVolumeRatio()
        {
            return smaVolume[0] > 0 ? Volume[0] / smaVolume[0] : 0;
        }

        private void SavePriorDayData()
        {
            if (CurrentBar > 0)
            {
                priorDayOpen = Open[0];
                priorDayHigh = High[0];
                priorDayLow = Low[0];
                priorDayClose = Close[0];

                if (dayDates.Count >= 20)
                {
                    dayDates.RemoveAt(0);
                    dayOpens.RemoveAt(0);
                    dayHighs.RemoveAt(0);
                    dayLows.RemoveAt(0);
                    dayCloses.RemoveAt(0);
                    dayVolumes.RemoveAt(0);
                }

                dayDates.Add(Time[0].Date);
                dayOpens.Add(Open[0]);
                dayHighs.Add(High[0]);
                dayLows.Add(Low[0]);
                dayCloses.Add(Close[0]);
                dayVolumes.Add(Volume[0]);
            }
        }

        private void ResetDailyParameters(DateTime tradingDay)
        {
            try
            {
                currentSessionDate = tradingDay;
                dailyStopHit = false;
                sessionEndExitLogged = false;
                dailyTradeCount = 0;
                totalCommission = 0.0;
                anchorLogged = false;
                isPinchFormed = false;
                handoffDetected = false;
                waitingForHandoffEntry = false;
                exitOrdersPlaced = false; // Reset bracket order flag
                totalFilledQuantity = 0; // Reset partial fill tracking
                intendedEntrySize = 0; // Reset intended entry size

                // ✅ DIFF 40: Clear persistent dictionaries at session reset
                tradeIdToMaxPnLMap.Clear();
                tradeIdToSignalMap.Clear();
                tradeIdToEntryTimeMap.Clear(); // ✅ DIFF 1: Clear EntryTime mapping
                mfeTrailLoggedTradeIds.Clear();
                lastMfeTrailExitReason = "";

                // ✅ DIFF 36: Unified TradeID and Logging Enforcement - Reset trade state
                currentTradeID = null;
                currentSignalName = string.Empty;
                exitLogged = false;
                trailExitSubmitted = false;

                // ✅ DIFF 41: Reset cached entry data at session reset
                cachedEntryPrice = 0.0;
                cachedEntryTime = DateTime.MinValue;
                isTradeActive = false;
                entryCapturedFallback = false;

                // ✅ DIFF 44: Reset trail logging enforcement flag
                mfeTrailShouldHaveLogged = false;

                // ✅ DIFF 53: Reset trail state in daily parameters
                exitLogged = false;
                mfeTrailArmed = false;
                currentTradeID = "";
                lastMfeTrailExitReason = "";
                if (tradeManager != null)
                    tradeManager.MaxFavorableExcursion = 0;

                // ✅ DIFF 6: Clear throttling variables at session reset
                lastLoggedResetTradeID = string.Empty;
                lastLoggedRecoverySignal = string.Empty;
                if (DebugMode && logManager != null)
                {
                    logManager.Log($"[Debug][DailyReset] exitOrdersPlaced reset to false", "Debug");
                    logManager.Log($"[Debug][DailyReset] totalFilledQuantity and intendedEntrySize reset to 0", "Debug");
                }
                // NOTE: We do NOT reset cumulativeStopHit here as it's designed to stop trading
                // for the entire strategy run once the cumulative drawdown limit is hit

                if (tradeManager != null) tradeManager.ResetDaily();
                if (statsTracker != null)
                {
                    statsTracker.ResetDailyStats();
                    if (logManager != null)
                        logManager.Log($"[Info][DailyReset] New Trading Day: {tradingDay:yyyy-MM-dd}, Risk Limits: MaxDaily=${MaxDailyLoss:F2}, MaxPerTrade=${MaxRiskPerTrade:F2}", "Info");
                }

                dayStartPrice = 0;
                priorHighVolumeDay = DateTime.MinValue;

                if (Bars != null && Bars.Count > 0 && Times != null && Times.Length > 0 &&
                    Times[0] != null && CurrentBars[0] >= 0 && CurrentBars[0] < Bars.Count)
                {
                    DateTime sessionBegin = tradingDay;
                    sessionStartTime = sessionBegin.AddHours(8).AddMinutes(30);
                    sessionEndTime = sessionStartTime.AddHours(6).AddMinutes(30);

                    if (logManager != null)
                        logManager.Log($"[Info][DailyReset] Session Times: Start={sessionStartTime:HH:mm:ss}, End={sessionEndTime:HH:mm:ss}", "Info");
                }
                else
                {
                    sessionStartTime = DateTime.Now.Date.AddHours(8).AddMinutes(30);
                    sessionEndTime = sessionStartTime.AddHours(6).AddMinutes(30);

                    if (logManager != null)
                        logManager.Log("[Warning] Could not configure session times with current bar data in ResetDailyParameters", "Warning");
                }

                if (avwap != null)
                {
                    SetAvwapAnchorType(avwap, AnchorTypeSelection);
                    logManager?.LogTradeEvent("DailyReset", Close[0], 0, $"New Trading Day: {tradingDay:yyyy-MM-dd}");
                    if (logManager != null)
                        logManager.Log($"[Init] Session Anchor Reset (Using {AnchorTypeSelection}): {AnchorTypeSelection} @ {tradingDay:yyyy-MM-dd}", "Info");
                }
                else
                {
                    if (logManager != null)
                        logManager.Log($"[Warning] Attempted to reset AVWAP anchor in ResetDailyParameters before AVWAP was initialized.", "Warning");
                }

                if (logManager != null)
                    logManager.Log($"[Info][DailyReset] Reset Complete: {tradingDay:yyyy-MM-dd}, Previous MaxDD: {statsTracker?.DailyMaxDrawdown:F2}", "Info");
            }
            catch (Exception ex)
            {
                if (logManager != null)
                    logManager.Log($"[Critical] ResetDailyParameters Error: {ex.Message}", "Critical");
            }
        }

        private void DetectMultiDaySetup()
        {
            if (dayCloses.Count < 2) return;

            double twoDaysAgoClose = dayCloses[dayCloses.Count - 2];
            double twoDaysAgoVolume = dayVolumes[dayCloses.Count - 2];
            bool significantMove = Math.Abs(priorDayClose - twoDaysAgoClose) / twoDaysAgoClose > 0.02 && twoDaysAgoVolume > smaVolume[0] * 2;

            isFirstDayOfMove = false;
            isSecondDayOfMove = significantMove;
            isThirdDayOfMove = dayCloses.Count >= 3 && Math.Abs(dayCloses[dayCloses.Count - 3] - twoDaysAgoClose) / twoDaysAgoClose > 0.02 && significantMove;

            if (isSecondDayOfMove)
            {
                secondDayOpenPrice = Open[0];
                priorHighVolumeDay = dayDates[dayCloses.Count - 2];
                logManager?.Log($"[Info] Second Day of Move Detected - Prior Close: {priorDayClose:F2}, Today Open: {secondDayOpenPrice:F2}, High Volume Day: {priorHighVolumeDay:yyyy-MM-dd}", "Info");
            }
            if (isThirdDayOfMove)
                logManager?.Log($"[Info] Third Day of Move Detected", "Info");
        }

        private void SetupAnchors()
        {
            if (avwap == null)
                return;

            SetAvwapAnchorType(avwap, AnchorTypeSelection);
            avwap.VolumeThresholdMultiplier = VolumeThresholdMultiplier;
            avwap.GapThresholdPercent = GapThresholdPercent;

            if (EnablePinchDetection && dayHighs.Count > 0 && dayLows.Count > 0)
            {
                int lookback = Math.Min(5, dayHighs.Count);
                double recentHigh = double.MinValue;
                double recentLow = double.MaxValue;
                DateTime highDate = DateTime.MinValue;
                DateTime lowDate = DateTime.MinValue;

                for (int i = 0; i < lookback; i++)
                {
                    int idx = dayHighs.Count - 1 - i;
                    if (dayHighs[idx] > recentHigh)
                    {
                        recentHigh = dayHighs[idx];
                        highDate = dayDates[idx];
                    }
                    if (dayLows[idx] < recentLow)
                    {
                        recentLow = dayLows[idx];
                        lowDate = dayDates[idx];
                    }
                }

                if (avwapPriorHigh != null && avwapPriorLow != null)
                {
                    AnchorAvwapToSpecificDay(avwapPriorHigh, highDate);
                    AnchorAvwapToSpecificDay(avwapPriorLow, lowDate);
                    logManager?.Log($"[Info] Pinch Anchors Set - High: {highDate:MM/dd} ({recentHigh:F2}), Low: {lowDate:MM/dd} ({recentLow:F2})", "Info");
                }
                else
                {
                    logManager?.Log($"[Warning] SetupAnchors: Pinch detection enabled but avwapPriorHigh or avwapPriorLow is null.", "Warning");
                }
            }

            if (avwapPriorDay != null)
                AnchorAvwapToPriorDay(avwapPriorDay);
            else
                logManager?.Log($"[Warning] SetupAnchors: avwapPriorDay is null.", "Warning");

            if (isSecondDayOfMove && priorHighVolumeDay != DateTime.MinValue)
            {
                if (avwapSecondDay != null)
                    AnchorAvwapToSpecificDay(avwapSecondDay, priorHighVolumeDay);
                else
                    logManager?.Log($"[Warning] SetupAnchors: isSecondDayOfMove true but avwapSecondDay is null.", "Warning");
            }
        }

        private void SetAvwapAnchorType(AnchoredVWAPIndicator targetAvwap, NinjaTrader.NinjaScript.AnchorTypeEnum anchorType)
        {
            targetAvwap.AnchorTypeSelection = anchorType;
            switch (anchorType)
            {
                case NinjaTrader.NinjaScript.AnchorTypeEnum.OpeningRange:
                    targetAvwap.AnchorStartTime = sessionStartTime.AddMinutes(30);
                    break;
                case NinjaTrader.NinjaScript.AnchorTypeEnum.SessionOpen:
                    targetAvwap.AnchorStartTime = sessionStartTime;
                    break;
                case NinjaTrader.NinjaScript.AnchorTypeEnum.PreviousDayClose:
                    AnchorAvwapToPriorDay(targetAvwap);
                    break;
                case NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar:
                case NinjaTrader.NinjaScript.AnchorTypeEnum.GapOpening:
                    targetAvwap.AnchorStartTime = sessionStartTime;
                    break;
                case NinjaTrader.NinjaScript.AnchorTypeEnum.Custom:
                    break;
            }
            targetAvwap.Reset();
        }

        private void AnchorAvwapToPriorDay(AnchoredVWAPIndicator targetAvwap)
        {
            if (targetAvwap == null || sessionIterator == null) return;
            DateTime previousTradingDay = sessionIterator.GetTradingDay(currentSessionDate.AddDays(-1));
            targetAvwap.AnchorStartTime = previousTradingDay.AddHours(16);
            targetAvwap.Reset();
        }

        private void AnchorAvwapToSpecificDay(AnchoredVWAPIndicator targetAvwap, DateTime day)
        {
            if (targetAvwap == null || day == DateTime.MinValue) return;
            targetAvwap.AnchorStartTime = day.AddHours(16);
            targetAvwap.Reset();
        }

        private void CheckForPinchFormation(double price)
        {
            if (!EnablePinchDetection || double.IsNaN(avwapPriorHigh[0]) || double.IsNaN(avwapPriorLow[0]) || double.IsNaN(avwap[0])) return;

            double highValue = avwapPriorHigh[0];
            double lowValue = avwapPriorLow[0];
            double currentValue = avwap[0];
            double range = Math.Abs(priorDayHigh - priorDayLow);
            pinchWidth = Math.Abs(highValue - lowValue) / range * 100;

            if (pinchWidth <= MinPinchWidthPercent && CurrentBar > 1 &&
                price >= lowValue && price <= highValue)
            {
                isPinchFormed = true;
                pinchFormationTime = Time[0];
                upperPinchValue = highValue;
                lowerPinchValue = lowValue;
                logManager?.Log($"[Info] AVWAP Pinch Formed: Width={pinchWidth:F2}%, Upper={upperPinchValue:F2}, Lower={lowerPinchValue:F2}", "Info");
            }
        }

        private void CheckForHandoffPattern(double price)
        {
            if (!EnableHandoffDetection || avwap == null || double.IsNaN(avwap[0]) || atr14 == null || double.IsNaN(atr14[0]) || CurrentBar < 2) return;

            double currentAVWAP = avwap[0];
            double priorAVWAP = avwap[1];
            double atrValue = atr14[0];
            double testTolerance = atrValue * 0.25;
            double bounceThresholdATR = atrValue * 0.5;
            const int handoffCooldownBars = 5;
            const int handoffLogThrottleBars = 20; // Only log handoffs every 20 bars at most

            bool testedAVWAPLong = Low[1] <= priorAVWAP + testTolerance || (CurrentBar > 2 && Low[2] <= avwap[2] + testTolerance);
            bool bouncedLong = Close[0] > currentAVWAP && (Close[0] - currentAVWAP) > bounceThresholdATR;
            bool slopeWasPositive = CurrentBar > 2 && priorAVWAP > avwap[2];

            if (testedAVWAPLong && bouncedLong && slopeWasPositive && (CurrentBar - lastHandoffBar > handoffCooldownBars))
            {
                int handoffBarIndex = -1;
                for (int i = 1; i <= Math.Min(3, CurrentBar - 1); i++)
                {
                    if (Low[i] <= priorAVWAP + testTolerance)
                    {
                        handoffBarIndex = CurrentBar - i;
                        break;
                    }
                }
                if (handoffBarIndex == -1) handoffBarIndex = CurrentBar - 1;

                handoffDetected = true;
                handoffTime = Time[handoffBarIndex];
                handoffPrice = Low[handoffBarIndex];
                waitingForHandoffEntry = true;

                // Only log handoffs if enough bars have passed since the last one
                if (CurrentBar - lastHandoffBar >= handoffLogThrottleBars)
                    logManager?.LogTradeEvent("Handoff", handoffPrice, 0, $"Long Handoff at Bar {handoffBarIndex}");

                lastHandoffBar = handoffBarIndex;
            }

            bool testedAVWAPShort = High[1] >= priorAVWAP - testTolerance || (CurrentBar > 2 && High[2] >= avwap[2] - testTolerance);
            bool bouncedShort = Close[0] < currentAVWAP && (currentAVWAP - Close[0]) > bounceThresholdATR && Close[1] >= currentAVWAP;
            bool slopeWasNegative = CurrentBar > 2 && priorAVWAP < avwap[2];

            if (testedAVWAPShort && bouncedShort && slopeWasNegative && (CurrentBar - lastHandoffBar > handoffCooldownBars))
            {
                int handoffBarIndex = -1;
                for (int i = 1; i <= Math.Min(3, CurrentBar - 1); i++)
                {
                    if (High[i] >= priorAVWAP - testTolerance)
                    {
                        handoffBarIndex = CurrentBar - i;
                        break;
                    }
                }
                if (handoffBarIndex == -1) handoffBarIndex = CurrentBar - 1;

                handoffDetected = true;
                handoffTime = Time[handoffBarIndex];
                handoffPrice = High[handoffBarIndex];
                waitingForHandoffEntry = true;

                // Only log handoffs if enough bars have passed since the last one
                if (CurrentBar - lastHandoffBar >= handoffLogThrottleBars)
                    logManager?.LogTradeEvent("Handoff", handoffPrice, 0, $"Short Handoff at Bar {handoffBarIndex}");

                lastHandoffBar = handoffBarIndex;
            }
        }

        /// <summary>
        /// Unified long stop price calculation using backsolved stop distance
        /// </summary>
        private double CalculateLongStopPrice(double entryPrice)
        {
            // ✅ DIFF 4A: Use backsolved stop distance from position sizing for perfect risk alignment
            double stopDistance = currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance();
            double stopPrice = entryPrice - stopDistance;

            // Pinch breakout adjustment (optional) - use fallback ATR for adjustment
            if (isPinchFormed && EntryMethodSelection == EntryMethod.PinchBreakout && lowerPinchValue < entryPrice)
            {
                double atrValue = atr1min != null && !double.IsNaN(atr1min[0]) && atr1min[0] > 0 ? atr1min[0] : atr14[0];
                stopPrice = Math.Max(stopPrice, lowerPinchValue - (atrValue * 0.2));
            }

            Print($"[UNIFIED_LONG_STOP] Entry={entryPrice:F2} | BacksolvedStopDist={stopDistance:F2} → StopPx={stopPrice:F2}");
            return stopPrice;
        }

        /// <summary>
        /// Unified short stop price calculation using backsolved stop distance
        /// </summary>
        private double CalculateShortStopPrice(double entryPrice)
        {
            // ✅ DIFF 4A: Use backsolved stop distance from position sizing for perfect risk alignment
            double stopDistance = currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance();
            double stopPrice = entryPrice + stopDistance;

            // Pinch breakout adjustment (optional) - use fallback ATR for adjustment
            if (isPinchFormed && EntryMethodSelection == EntryMethod.PinchBreakout && upperPinchValue > entryPrice)
            {
                double atrValue = atr1min != null && !double.IsNaN(atr1min[0]) && atr1min[0] > 0 ? atr1min[0] : atr14[0];
                stopPrice = Math.Min(stopPrice, upperPinchValue + (atrValue * 0.2));
            }

            Print($"[UNIFIED_SHORT_STOP] Entry={entryPrice:F2} | BacksolvedStopDist={stopDistance:F2} → StopPx={stopPrice:F2}");
            return stopPrice;
        }

        /// <summary>
        /// Fallback stop distance calculation when currentStopDistance is not available
        /// </summary>
        private double GetFallbackStopDistance()
        {
            double atrValue = atr1min != null && !double.IsNaN(atr1min[0]) && atr1min[0] > 0
                ? atr1min[0]
                : atr14[0]; // Fallback to 14-period ATR

            double fallbackDistance = atrValue * StopDistanceATR;
            Print($"[FALLBACK_STOP] Using fallback stop distance: {fallbackDistance:F2} (ATR={atrValue:F2} * {StopDistanceATR})");
            return fallbackDistance;
        }

        private void WriteFinalSummary()
        {
            Print("WriteFinalSummary entered.");
            bool tradesExist = SystemPerformance?.AllTrades != null && SystemPerformance.AllTrades.Count > 0;
            Print($"WriteFinalSummary: tradesExist = {tradesExist}");
            bool logManagerExists = logManager != null;
            Print($"WriteFinalSummary: logManagerExists = {logManagerExists}");

            // --- Analyze for Target Hit Issues Across All Trades ---
            // This runs once at the end of the backtest/optimization
            if (tradesExist && logManager != null && AnalyzeTargetHits && BarsArray[1] != null && atr14 != null) // Check Analyze flag and data readiness
            {
                int likelyHitButLoss = 0;
                // Loop through every completed trade
                foreach (Trade trade in SystemPerformance.AllTrades)
                {
                    bool likelyTargetHit = false;
                    bool wasLong = trade.Entry.MarketPosition == MarketPosition.Long;
                    // Estimate target price using current ATR as approximation
                    double targetPrice = wasLong
                        ? trade.Entry.Price + (atr14[0] * ProfitTargetATR)
                        : trade.Entry.Price - (atr14[0] * ProfitTargetATR);

                    // Ensure bar indices are valid
                    if (trade.Entry.BarIndex >= 0 && trade.Exit.BarIndex >= trade.Entry.BarIndex && trade.Exit.BarIndex < BarsArray[1].Count)
                    {
                        // Check 1-min bars during the trade
                        for (int i = trade.Entry.BarIndex; i <= trade.Exit.BarIndex; i++)
                        {
                            try
                            {
                                if (wasLong && BarsArray[1].GetHigh(i) >= targetPrice)
                                {
                                    likelyTargetHit = true;
                                    break;
                                }
                                else if (!wasLong && BarsArray[1].GetLow(i) <= targetPrice)
                                {
                                    likelyTargetHit = true;
                                    break;
                                }
                            }
                            catch { /* Ignore potential bar access errors during final summary */ }
                        }
                    }

                    // Increment counter if target was likely hit but PnL wasn't positive
                    if (likelyTargetHit && trade.ProfitCurrency <= 0)
                        likelyHitButLoss++;
                }

                // If any such trades were found, log a summary warning
                if (likelyHitButLoss > 0)
                {
                    logManager?.Log($"[Warning] Post-Backtest Analysis: {likelyHitButLoss} trade(s) likely hit target price based on 1-min bars, but were recorded as breakeven/loss. Review backtest fill assumptions.", "Warning");
                }
            }
            // --- End Analyze for Target Hit Issues ---

            // --- Write Human-Readable Log (Existing Logic) ---
            // Text summary logic removed due to compilation errors/conflicts.
            // Focusing on the CSV summary output below.

            // --- Write CSV Summary ---
            Print($"WriteFinalSummary: Checking CSV write. csvSummaryPath = '{csvSummaryPath}', tradesExist = {tradesExist}");

            if (!string.IsNullOrEmpty(csvSummaryPath) && tradesExist)
            {
                Print($"WriteFinalSummary: Attempting to write CSV to {csvSummaryPath}");
                try
                {
                    TradeCollection trades = SystemPerformance.AllTrades;
                    List<string> csvHeaders = new List<string>();
                    List<string> csvValues = new List<string>();

                    int winningTrades = 0;
                    double grossProfit = 0;
                    double grossLoss = 0;
                    foreach (Trade trade in trades)
                    {
                        double tradePnL = trade.ProfitCurrency;
                        if (tradePnL > 0)
                        {
                            winningTrades++;
                            grossProfit += tradePnL;
                        }
                        else if (tradePnL < 0)
                        {
                            grossLoss += Math.Abs(tradePnL);
                        }
                    }
                    double winRate = trades.Count > 0 ? (double)winningTrades / trades.Count * 100 : 0;

                    csvHeaders.AddRange(new[] {
                        "Timestamp", "StrategyName", "Instrument", "NetProfit", "TotalTrades",
                        "WinRate", "GrossProfit", "GrossLoss", "TotalCommission", "MaxStrategyDrawdown",
                        "ParamSetID"
                    });
                    csvValues.AddRange(new[] {
                        EscapeCsvValue(DateTime.Now),
                        EscapeCsvValue(Name),
                        EscapeCsvValue(Instrument?.FullName ?? "N/A"),
                        trades.TradesPerformance.Currency.CumProfit.ToString("F2"),
                        trades.Count.ToString(),
                        winRate.ToString("F2"),
                        grossProfit.ToString("F2"),
                        grossLoss.ToString("F2"),
                        trades.TradesPerformance.TotalCommission.ToString("F2"),
                        trades.TradesPerformance.Currency.Drawdown.ToString("F2"),
                        runID
                    });

                    PropertyInfo[] properties = this.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                            .Where(p => p.GetCustomAttribute<System.ComponentModel.DataAnnotations.DisplayAttribute>() != null && p.CanRead)
                                            .OrderBy(p => p.Name)
                                            .ToArray();

                    foreach (PropertyInfo prop in properties)
                    {
                        if (!csvHeaders.Contains(prop.Name))
                        {
                            csvHeaders.Add(prop.Name);
                            object value = prop.GetValue(this);
                            csvValues.Add(EscapeCsvValue(value));
                        }
                    }
                    Print($"WriteFinalSummary: Parameter reflection for CSV successful. Total Headers: {csvHeaders.Count}");

                    string headerLine = string.Join(",", csvHeaders);
                    string dataLine = string.Join(",", csvValues);

                    lock (csvFileLock)
                    {
                        Print($"WriteFinalSummary: Acquired lock for {csvSummaryPath}");
                        bool fileExists = File.Exists(csvSummaryPath);
                        Print($"WriteFinalSummary: File exists check ({csvSummaryPath}): {fileExists}");
                        using (StreamWriter writer = new StreamWriter(csvSummaryPath, true, Encoding.UTF8))
                        {
                            Print($"WriteFinalSummary: StreamWriter opened for {csvSummaryPath}");
                            if (!fileExists || new FileInfo(csvSummaryPath).Length == 0)
                            {
                                Print($"WriteFinalSummary: Writing header to {csvSummaryPath}");
                                writer.WriteLine(headerLine);
                            }
                            Print($"WriteFinalSummary: Writing data to {csvSummaryPath}");
                            writer.WriteLine(dataLine);
                            Print($"WriteFinalSummary: Data written to {csvSummaryPath}");
                        }
                        Print($"WriteFinalSummary: Released lock for {csvSummaryPath}");
                    }
                    logManager?.Log($"[Info] CSV Summary row appended to {csvSummaryPath}", "Info");
                    Print($"WriteFinalSummary: CSV write process completed successfully for {csvSummaryPath}");
                }
                catch (Exception ex) // Ensure catch block is correctly formed
                {
                    Print($"[ERROR] WriteFinalSummary: Failed to write CSV summary row: {ex.Message}\nStackTrace: {ex.StackTrace}");
                    logManager?.Log($"[ERROR] Failed to write CSV summary row: {ex.Message}", "Error");
                }
            } // End of if (!string.IsNullOrEmpty(csvSummaryPath) && tradesExist)
            else // Condition for not writing CSV
            {
                Print($"WriteFinalSummary: CSV not written. Path Empty: {string.IsNullOrEmpty(csvSummaryPath)}, No Trades: {!tradesExist}");
            }
            // --- END Write CSV Summary ---
            Print("WriteFinalSummary finished.");
        }

        private AnchorTypeEnum DetermineAnchorType()
        {
            double gapPct = Math.Abs(Open[0] - Close[1]) / Close[1] * 100;
            bool highVolBar = smaVolume[0] > 0 && Volume[0] > smaVolume[0] * VolumeThresholdMultiplier;

            if (isPinchFormed)
                return NinjaTrader.NinjaScript.AnchorTypeEnum.Custom;
            if (gapPct >= GapThresholdPercent)
                return NinjaTrader.NinjaScript.AnchorTypeEnum.GapOpening;
            if (highVolBar)
                return NinjaTrader.NinjaScript.AnchorTypeEnum.HighVolumeBar;
            if (Time[0].TimeOfDay < new TimeSpan(9, 0, 0))
                return NinjaTrader.NinjaScript.AnchorTypeEnum.SessionOpen;
            return NinjaTrader.NinjaScript.AnchorTypeEnum.OpeningRange;
        }

        private void EvaluateMarketConditionsAndSetEntryMethod()
        {
            if (CurrentBar < 20) return;

            double slope = avwapSlope[0];
            double adx = adx14[0];
            double atr = atr14[0];
            double volRatio = GetVolumeRatio();
            double prevClose = dayCloses.Count > 0 ? dayCloses.Last() : Close[1];
            double gapPercent = Math.Abs(Open[0] - prevClose) / prevClose * 100;

            bool trendingUp = slope > 0.1 && Close[0] > SMA(Close, 20)[0] && adx > MinADXForEntry;
            bool trendingDown = slope < -0.1 && Close[0] < SMA(Close, 20)[0] && adx > MinADXForEntry;
            bool rangebound = adx < MinADXForEntry && StdDev(Close, 14)[0] < atr;
            bool breakoutSetup = High[0] > High[1] + atr * 1.5;
            bool gapOpen = gapPercent > GapThresholdPercent;
            bool highVolStart = smaVolume[0] > 0 && Volume[0] > smaVolume[0] * VolumeThresholdMultiplier;

            EntryMethod previousMethod = EntryMethodSelection;

            if (isPinchFormed)
                EntryMethodSelection = EntryMethod.PinchBreakout;
            else if (handoffDetected && slope > 0)
                EntryMethodSelection = EntryMethod.Handoff;
            else if (trendingUp)
                EntryMethodSelection = EntryMethod.Momentum;
            else if (trendingDown)
                EntryMethodSelection = EntryMethod.Touch;
            else if (rangebound)
                EntryMethodSelection = EntryMethod.GreenToRed;
            else if (breakoutSetup)
                EntryMethodSelection = EntryMethod.PinchBreakout;
            else if (gapOpen || highVolStart)
                EntryMethodSelection = EntryMethod.Momentum;
            else
                EntryMethodSelection = EntryMethod.Touch;

            if (EntryMethodSelection != previousMethod && DebugMode)
                logManager?.Log($"[Dynamic] Entry changed to: {EntryMethodSelection.ToString()} @ {Time[0]:HH:mm:ss} (TrendingUp={trendingUp}, TrendingDown={trendingDown}, Rangebound={rangebound}, Breakout={breakoutSetup}, Gap={gapOpen}, HighVol={highVolStart})", "Debug");
        }

        private void ExportSignal(DateTime time, string signalName, double price)
        {
            int positionSize = tradeManager.CalculatePositionSize(price);
            double stopPrice = Position.MarketPosition == MarketPosition.Long ? CalculateLongStopPrice(price) : CalculateShortStopPrice(price);
            double targetPrice = Position.MarketPosition == MarketPosition.Long ? price + (atr14[0] * ProfitTargetATR) : price - (atr14[0] * ProfitTargetATR);
            // Always N/A since we don't have TimeframeManager
            string mtfAlignment = "N/A";
            string signalLine = $"{time:yyyy-MM-dd HH:mm:ss},{CurrentBar},{EntryMethodSelection},{price:F2},{positionSize},{stopPrice:F2},{targetPrice:F2}," +
                                $"{avwap[0]:F2},{avwapSlope[0]:F4},{atr14[0]:F2},{adx14[0]:F2},{GetVolumeRatio():F2}," +
                                $"{Math.Abs(Open[0] - (dayCloses.Count > 0 ? dayCloses.Last() : Close[1])) / (dayCloses.Count > 0 ? dayCloses.Last() : Close[1]) * 100:F2}," +
                                $"{(isPinchFormed ? 1 : 0)},{(handoffDetected ? 1 : 0)},{mtfAlignment}";
            lock (orderLock)
            {
                if (!File.Exists(signalExportPath))
                {
                    File.WriteAllText(signalExportPath, "Timestamp,BarNumber,EntryMethod,Price,PositionSize,StopPrice,TargetPrice,AVWAPValue,Slope,ATR,ADX,VolumeRatio,GapPercent,PinchFormed,HandoffDetected,MTFAlignment\n");
                }
                File.AppendAllText(signalExportPath, signalLine + "\n");
            }
        }

        private string DetermineExitReason(double finalPnL, DateTime exitTime)
        {
            string exitReason = "Unknown";

            // Check for session end exit
            if (exitTime >= sessionEndTime.AddMinutes(-10)) // Within 10 minutes of session end
            {
                exitReason = "SessionEnd";
            }
            // Check for stop loss (significant loss)
            else if (finalPnL <= -MaxRiskPerTrade * 0.9)
            {
                exitReason = "StopLoss";
            }
            // Check for daily loss limit
            else if (finalPnL <= -MaxDailyLoss * 0.9)
            {
                exitReason = "DailyLossLimit";
            }
            // Check for any loss
            else if (finalPnL < 0)
            {
                exitReason = "LossExit";
            }
            // Check for MFE trail exit (profitable and trail was armed)
            else if (EnableMFETrail && mfeTrailArmed && finalPnL > 0)
            {
                exitReason = "MFE_Trail";
            }
            // Check for target hit (significant profit without MFE trail)
            else if (finalPnL >= MaxRiskPerTrade * 2.0)
            {
                exitReason = "TargetHit";
            }
            // Check for breakeven exit
            else if (Math.Abs(finalPnL) < MaxRiskPerTrade * 0.1) // Within 10% of breakeven
            {
                exitReason = "Breakeven";
            }
            // Default to manual exit for other cases
            else
            {
                exitReason = "ManualExit";
            }

            return exitReason;
        }

        // ✅ DIFF 40: LogExitReason method removed - replaced by WriteMFETrailLogOnce for consistent logging

        // WriteSmartTradeValidationEntry method removed - replaced by WriteMFETrailLogOnce



        // ✅ DIFF 714: Helper method for timestamped logging
        private void LogWithTime(string message)
        {
            if (Bars != null && Bars.Count > 0 && Times != null && Times[0].Count > 0)
                Print($"{Time[0]:HH:mm:ss} {message}");
            else
                Print($"{DateTime.Now:HH:mm:ss} {message}");
        }

        // ✅ DIFF 37: Enhanced MFE trail logging with precision, reasoning, and deduplication
        private void WriteMFETrailLogOnce(string tradeId, string signalName, double entryPrice, double exitPrice, DateTime entryTime, DateTime exitTime, double maxPnLDuringTrade, double finalPnl, string reason, bool wasOverride, double frozenMfeOnArm = 0.0, double triggerPnL = 0.0)
        {
            if (string.IsNullOrEmpty(tradeId))
            {
                Print($"[LOG_ERROR] WriteMFETrailLogOnce: tradeId is null/empty. Reason: {reason}");
                return;
            }
            if (mfeTrailLoggedTradeIds.Contains(tradeId))
            {
                Print($"[TRAIL_LOG_SKIP] TradeID already written: {tradeId}");
                return;
            }

            // Write header if needed
            if (!mfeTrailHeaderWritten)
                WriteMFETrailLogHeaderIfNeeded();

            mfeTrailLoggedTradeIds.Add(tradeId);
            lastMfeTrailExitReason = reason;

            // ✅ DIFF 53: Enhanced MFE fallback logic with NT8 Trade.Mfe
            double maxPnLForLog = maxPnLDuringTrade;
            if (tradeIdToMaxPnLMap.ContainsKey(tradeId))
            {
                maxPnLForLog = tradeIdToMaxPnLMap[tradeId];
            }

            // Use NT8 fallback if ours is missing or stale
            Trade lastTrade = SystemPerformance?.AllTrades?.Count > 0 ? SystemPerformance.AllTrades.Last() : null;
            if (maxPnLForLog <= 0 && lastTrade != null && lastTrade.ProfitCurrency > 0)
            {
                // Use the final PnL as a fallback estimate for MFE when our tracking is missing
                maxPnLForLog = lastTrade.ProfitCurrency;
                Print($"[MFE Fallback Applied] Used NT8.ProfitCurrency={lastTrade.ProfitCurrency:F2} as MaxPnL fallback");
            }

            // ✅ DIFF 39: Enhanced capture ratio calculation for console output
            double captureRatio = maxPnLForLog != 0 ? Math.Round(100.0 * finalPnl / maxPnLForLog, 1) : 0.0;
            string overrideNote = wasOverride ? "OVERRIDE" : "Auto";

            // ✅ DIFF 18: Normalize timestamps to the minute (strip seconds for consistent NT8 reconciliation)
            string entryTimeFormatted = entryTime.ToString("yyyy-MM-dd HH:mm");
            string exitTimeFormatted = exitTime.ToString("yyyy-MM-dd HH:mm");

            // Enhanced CSV formatting with proper decimal places and capture percentage
            string logLine = string.Format("{0},{1},{2:F2},{3:F2},{4},{5},{6:F2},{7:F2},{8},{9},{10:F1},{11:F2},{12:F2}",
                tradeId,
                signalName,
                entryPrice,
                exitPrice,
                entryTimeFormatted,
                exitTimeFormatted,
                maxPnLForLog,
                finalPnl,
                reason,
                wasOverride ? "1" : "0",
                captureRatio,
                frozenMfeOnArm,
                triggerPnL
            );

            try
            {
                string logPath = System.IO.Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs", "EnhancedAVWAP", "MFETrailLogs.csv");

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(logPath));

                using (var writer = new System.IO.StreamWriter(logPath, true))
                {
                    writer.WriteLine(logLine);
                }

                Print($"[MFETrailCSV_WriteOnce] ✅ Logged {tradeId} | Reason={reason} | PnL={finalPnl:F2} | Capture={captureRatio:F1}% | {overrideNote}");
            }
            catch (Exception ex)
            {
                Print($"[LOG_ERROR] WriteMFETrailLogOnce: Failed to write trail log — {ex.Message}");
            }
        }

        // ✅ DIFF 34: Helper method to write MFE trail CSV header once
        private void WriteMFETrailLogHeaderIfNeeded()
        {
            if (mfeTrailHeaderWritten) return;
            mfeTrailHeaderWritten = true;

            try
            {
                string logPath = System.IO.Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs", "EnhancedAVWAP", "MFETrailLogs.csv");

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(logPath));

                string header = "TradeID,SignalName,EntryPrice,ExitPrice,EntryTime,ExitTime,MaxPnLDuringTrade,FinalPnL,Reason,WasOverride,CapturePct,FrozenMFEOnArm,TriggerPnL";

                using (var writer = new System.IO.StreamWriter(logPath, false)) // false = overwrite existing file
                {
                    writer.WriteLine(header);
                }

                Print($"[MFETrailCSV_Header] ✅ Header written to MFETrailLogs.csv");
            }
            catch (Exception ex)
            {
                Print($"[LOG_ERROR] Failed to write MFETrail header: {ex.Message}");
            }
        }
        #endregion

        #region Nested Classes
        internal class TradeManager
        {
            private readonly EnhancedAnchoredVWAPStrategy strategy;
            public int ContractsTradedToday { get; private set; }
            public double EntryPrice { get; set; } // Ensure setter is public (default for public property)
            public int EntryBar { get; private set; }
            public DateTime EntryTime { get; set; } // Added for MFE Trail
            public double MaxFavorableExcursion { get; set; } // Added for MFE Trail
            private int entryQuantity;
            public MarketPosition EntryDirection { get; private set; }
            public double StopPrice { get; set; }
            public bool exitLogged = false;
            // Removed unused TargetOrder fields

            public TradeManager(EnhancedAnchoredVWAPStrategy strategy)
            {
                this.strategy = strategy;
            }

            public void ResetDaily()
            {
                ContractsTradedToday = 0;
                EntryPrice = 0;
                EntryBar = 0;
                entryQuantity = 0;
                EntryDirection = MarketPosition.Flat;
                StopPrice = 0;
                exitLogged = false;
                // Removed ResetTargetOrder call - fields no longer exist
            }

            // Removed SetTargetOrder method - target price is calculated in ManageOpenPositionWithBracket

            // Removed IsTargetOrderActive method - not used

            // Removed ResetTargetOrder method - fields no longer exist

            /// <summary>
            /// Unified position sizing with backsolved stop distance based on MaxRiskPerTrade
            /// </summary>
            public int CalculatePositionSize(double entryPrice)
            {
                // ✅ DIFF 4A: Use 1-minute ATR for initial stop distance estimation
                double atrValue = strategy.atr1min != null && !double.IsNaN(strategy.atr1min[0]) && strategy.atr1min[0] > 0
                    ? strategy.atr1min[0]
                    : strategy.atr14[0]; // Fallback to 14-period ATR

                if (double.IsNaN(atrValue) || atrValue == 0)
                {
                    strategy.logManager?.Log("[Warning] Invalid ATR in CalculatePositionSize, using default size 1.", "Warning");
                    return 1;
                }

                // ✅ DIFF 4A: Initial stop distance calculation for sizing estimation
                double initialStopDistance = atrValue * strategy.StopDistanceATR;
                if (initialStopDistance == 0)
                {
                    strategy.logManager?.Log("[Warning] Calculated Stop Distance is zero in CalculatePositionSize, using default size 1.", "Warning");
                    return 1;
                }

                // ✅ DIFF 4A: Estimated contract risk for initial sizing
                double estimatedContractRisk = initialStopDistance * strategy.instrumentPointValue;
                if (estimatedContractRisk <= 0)
                {
                    strategy.logManager?.Log($"[Warning] Invalid EstimatedContractRisk ({estimatedContractRisk:F2}) in CalculatePositionSize, using default size 1.", "Warning");
                    return 1;
                }

                // ✅ DIFF 4A: Calculate position size based on estimated risk
                int basePositionSize = (int)Math.Floor(strategy.MaxRiskPerTrade / estimatedContractRisk);
                int maxContractsCap = 10; // Safety cap
                int finalPositionSize = Math.Max(1, Math.Min(basePositionSize, maxContractsCap));

                // ✅ DIFF 4A: Backsolve the actual stop distance based on final position size and MaxRiskPerTrade
                double actualStopDistance = strategy.MaxRiskPerTrade / (finalPositionSize * strategy.instrumentPointValue);

                // Store the backsolved stop distance for use in stop price calculations
                strategy.currentStopDistance = actualStopDistance;

                // ✅ DIFF 4A: Comprehensive logging for unified risk calculations
                strategy.Print($"[UNIFIED_SIZING] ATR={atrValue:F2} | InitialStopDist={initialStopDistance:F2} | EstimatedRisk=${estimatedContractRisk:F2} | Size={finalPositionSize} | BacksolvedStopDist={actualStopDistance:F2} | ActualRisk=${actualStopDistance * finalPositionSize * strategy.instrumentPointValue:F2}");

                if (basePositionSize > maxContractsCap && strategy.DebugMode)
                {
                    strategy.logManager?.Log($"[Debug][CalcPosSize] Size Capped: Base={basePositionSize}, Cap={maxContractsCap}, Final={finalPositionSize}", "Debug");
                }

                return finalPositionSize;
            }

            public void OnExecutionUpdate(Execution execution, double adjustedPrice)
            {
                // Fortress Guard Layer 4 — Failsafe (simplified)
                if (execution.Order != null && execution.Order.OrderState == OrderState.Filled)
                {
                    if ((execution.Order.Name?.Contains("Exit") == true || execution.Order.Name?.Contains("Trail") == true))
                    {
                        strategy.Print($"[Failsafe] Exit filled for {execution.Order.Name} — logging handled by WriteMFETrailLogOnce.");
                    }
                }
                if (execution.Order.OrderState != OrderState.Filled) return;

                ContractsTradedToday += execution.Quantity;

                // ✅ DIFF 50: Only reset MFE on ENTRY fills, not exit fills
                if (execution.Order.OrderAction == OrderAction.Buy || execution.Order.OrderAction == OrderAction.SellShort)
                {
                    // Reset MFE only on new entry fills
                    MaxFavorableExcursion = 0;
                    strategy.Print($"[MFE_RESET] New entry fill - MFE reset to 0 for order: {execution.Order.Name}");
                }
                else
                {
                    strategy.Print($"[MFE_PRESERVE] Exit fill detected - preserving MFE: {MaxFavorableExcursion:F2} for order: {execution.Order.Name}");
                }

                if (execution.Order.OrderAction == OrderAction.Buy || execution.Order.OrderAction == OrderAction.SellShort)
                {
                    EntryPrice = execution.Price;
                    EntryBar = strategy.CurrentBar;
                    EntryTime = strategy.Time[0]; // Set entry time for MFE Trail
                    entryQuantity = execution.Quantity;
                    EntryDirection = execution.Order.OrderAction == OrderAction.Buy ? MarketPosition.Long : MarketPosition.Short;
                    StopPrice = EntryDirection == MarketPosition.Long ? strategy.CalculateLongStopPrice(EntryPrice) :
                                strategy.CalculateShortStopPrice(EntryPrice);

                    // ✅ DIFF 3: Defensive risk validation after entry fill
                    double actualStopDistance = Math.Abs(EntryPrice - StopPrice);
                    double actualRiskPerContract = actualStopDistance * strategy.instrumentPointValue;
                    double totalActualRisk = actualRiskPerContract * entryQuantity;

                    strategy.Print($"[RISK_VALIDATION] Entry={EntryPrice:F2} | Stop={StopPrice:F2} | Distance={actualStopDistance:F2} | Risk/Contract=${actualRiskPerContract:F2} | TotalRisk=${totalActualRisk:F2} | MaxAllowed=${strategy.MaxRiskPerTrade:F2}");

                    if (totalActualRisk > strategy.MaxRiskPerTrade * 1.05) // 5% tolerance for rounding
                    {
                        strategy.Print($"[WARNING] Actual trade risk ${totalActualRisk:F2} exceeds MaxRiskPerTrade ${strategy.MaxRiskPerTrade:F2} by {((totalActualRisk / strategy.MaxRiskPerTrade - 1) * 100):F1}%");
                        strategy.logManager?.Log($"[WARNING] Trade risk validation failed: Actual=${totalActualRisk:F2} > Max=${strategy.MaxRiskPerTrade:F2}", "Warning");
                    }
                    else
                    {
                        strategy.Print($"[RISK_OK] Trade risk ${totalActualRisk:F2} is within MaxRiskPerTrade ${strategy.MaxRiskPerTrade:F2}");
                    }
                }
            }

            public void ManageStopLoss(double currentPrice)
            {
            }

            /// <summary>
            /// Cancel all active bracket orders (stops and targets) to prevent conflicts
            /// </summary>
            public void CancelActiveOrders()
            {
                try
                {
                    // Cancel all working orders that are bracket-related
                    var ordersToCancel = strategy.Orders.Where(o =>
                        o.OrderState == OrderState.Working &&
                        (o.Name.Contains("_Stop") || o.Name.Contains("_Target"))).ToList();

                    foreach (var order in ordersToCancel)
                    {
                        strategy.CancelOrder(order);
                        strategy.Print($"[CancelActiveOrders] Canceled order: {order.Name}");
                    }

                    if (ordersToCancel.Count > 0)
                    {
                        strategy.Print($"[CancelActiveOrders] Canceled {ordersToCancel.Count} active bracket orders");
                    }
                }
                catch (Exception ex)
                {
                    strategy.Print($"[ERROR] CancelActiveOrders failed: {ex.Message}");
                }
            }

            public void ExitPosition(string exitName, string fromEntrySignal, DateTime time)
            {
                // Only check for flat position, exitLogged is now set before calling this method
                if (strategy.Position.MarketPosition == MarketPosition.Flat) return;

                // Calculate PnL before exit occurs
                double unrealizedPnL = strategy.Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency);
                // Calculate realized PnL based on entry and current price
                double realizedPnL = 0;

                if (EntryPrice > 0)
                {
                    // For long positions: (currentPrice - entryPrice) * quantity * pointValue
                    // For short positions: (entryPrice - currentPrice) * quantity * pointValue
                    double priceChange = EntryDirection == MarketPosition.Long ?
                                        (strategy.Close[0] - EntryPrice) :
                                        (EntryPrice - strategy.Close[0]);
                    realizedPnL = priceChange * strategy.Position.Quantity * strategy.instrumentPointValue;
                }

                // ✅ DIFF 41: Enhanced exit logging with proper override detection for system-forced exits
                string actualExitReason = strategy.DetermineExitReason(realizedPnL, time);

                // Use WriteMFETrailLogOnce for consistent exit logging
                if (!string.IsNullOrEmpty(strategy.currentTradeID))
                {
                    // Determine if this is a system-forced exit (override)
                    bool isSystemForced = exitName.Contains("MaxDailyLoss") ||
                                         exitName.Contains("SessionClose") ||
                                         exitName.Contains("MaxRiskPerTrade") ||
                                         actualExitReason == "SessionEnd" ||
                                         actualExitReason == "DailyLossLimit";

                    // ✅ DIFF 2: Use stored execution time for accurate entry timestamp
                    DateTime entryTime = strategy.tradeIdToEntryTimeMap.ContainsKey(strategy.currentTradeID)
                        ? strategy.tradeIdToEntryTimeMap[strategy.currentTradeID]
                        : EntryTime; // fallback if not found

                    strategy.WriteMFETrailLogOnce(
                        strategy.currentTradeID,
                        strategy.currentSignalName,
                        EntryPrice,
                        strategy.Close[0],
                        entryTime,
                        time,
                        MaxFavorableExcursion,
                        realizedPnL,
                        actualExitReason == "StopLoss" ? "InitialATRStop" : actualExitReason,
                        isSystemForced
                    );
                }

                // Log exit event *before* submitting exit order to capture state
                strategy.logManager?.LogTradeEvent("Exit", strategy.Close[0], strategy.Position.Quantity, $"ForcedExit: {exitName} (Reason: {actualExitReason})", 0, 0, realizedPnL, unrealizedPnL);

                // Manual stop order cancellation removed as SetStopLoss is now used

                if (EntryDirection == MarketPosition.Long)
                    strategy.ExitLong(0, strategy.Position.Quantity, exitName, fromEntrySignal);
                else
                    strategy.ExitShort(0, strategy.Position.Quantity, exitName, fromEntrySignal);

                strategy.lastExitPnL = realizedPnL;
            }
        }

        internal class LogManager
        {
            private readonly EnhancedAnchoredVWAPStrategy strategy;
            private StreamWriter tradeLogWriter;
            private StreamWriter debugLogWriter;
            private readonly string tradeLogPath;
            private readonly string debugLogPath;
            public bool summaryWrittenForRun = false;
            private int lastLogBar = -1;
            private const int LogIntervalBars = 10;
            private int lastDebugBar = -1;
            private const int DebugThrottleBars = 10;
            private readonly HashSet<string> criticalTags = new HashSet<string> { "Critical", "Error", "Exit", "Entry", "Risk", "Trade", "AVWAP_Violation" };


            public LogManager(EnhancedAnchoredVWAPStrategy strategy, string fileSetID)
            {
                this.strategy = strategy;
                string basePath = Path.Combine(NinjaTrader.Core.Globals.UserDataDir, "logs", "EnhancedAVWAP");
                Directory.CreateDirectory(basePath);

                string tradeLogFile = $"TradeLog_{fileSetID}.txt";
                string debugLogFile = $"DebugLog_{fileSetID}.txt";
                tradeLogPath = Path.Combine(basePath, tradeLogFile);
                debugLogPath = Path.Combine(basePath, debugLogFile);
            }

            public void InitializeLogs()
            {
                // ✅ ALWAYS initialize trade log writer for unconditional trade logging
                tradeLogWriter = new StreamWriter(tradeLogPath, false) { AutoFlush = true };
                tradeLogWriter.WriteLine("Timestamp,EventType,BarNumber,Price,PositionSize,EntryMethod,AnchorType,AVWAPValue,Slope,ATR,ADX,VolumeRatio,PnL,UnrealizedPnL,TradeDuration,StopPrice,TargetPrice,Commission,Reason,ParamSetID");

                // Debug log still respects EnableLogging and DebugMode flags
                if (strategy.EnableLogging && strategy.DebugMode)
                {
                    debugLogWriter = new StreamWriter(debugLogPath, false) { AutoFlush = true };
                    debugLogWriter.WriteLine("Timestamp,BarNumber,Level,Category,Value1,Value2,Message");
                }
            }

            public void LogTradeEvent(string eventType, double price, int positionSize, string reason, double stopPrice = 0, double targetPrice = 0, double realizedPnL = 0, double unrealizedPnL = 0)
            {
                // ✅ ALWAYS log trade events regardless of EnableLogging flag - this is the final unlock!
                if (tradeLogWriter == null) return;
                string currentTimestamp = strategy.Time[0].ToString("yyyy-MM-dd HH:mm:ss"); // Get timestamp once

                lock (strategy.orderLock)
                {
                    try
                    { // Added try-catch
                        // Use provided unrealizedPnL if specified, otherwise calculate it
                        if (unrealizedPnL == 0)
                        {
                            unrealizedPnL = (eventType == "Exit" || eventType == "Critical" || eventType == "AVWAP_Violation") && strategy.Position.MarketPosition != MarketPosition.Flat
                                                ? strategy.Position.GetUnrealizedProfitLoss(PerformanceUnit.Currency) : 0;
                        }
                        int tradeDuration = (eventType == "Exit" || eventType == "TradeResult" || eventType == "Critical" || eventType == "AVWAP_Violation") && strategy.tradeManager.EntryBar > 0
                                                ? strategy.CurrentBar - strategy.tradeManager.EntryBar : 0;

                        // Enhanced formatting with NaN checks
                        tradeLogWriter.WriteLine($"{currentTimestamp},{eventType},{strategy.CurrentBar},{price:F2},{positionSize}," +
                                                 $"{strategy.EntryMethodSelection},{strategy.AnchorTypeSelection}," +
                                                 $"{(strategy.avwap != null && !double.IsNaN(strategy.avwap[0]) ? strategy.avwap[0].ToString("F2") : "NaN")}," +
                                                 $"{(strategy.avwapSlope != null && !double.IsNaN(strategy.avwapSlope[0]) ? strategy.avwapSlope[0].ToString("F4") : "NaN")}," +
                                                 $"{(strategy.atr14 != null && !double.IsNaN(strategy.atr14[0]) ? strategy.atr14[0].ToString("F2") : "NaN")}," +
                                                 $"{(strategy.adx14 != null && !double.IsNaN(strategy.adx14[0]) ? strategy.adx14[0].ToString("F2") : "NaN")}," +
                                                 $"{(strategy.smaVolume != null && strategy.smaVolume.Count > 0 && !double.IsNaN(strategy.smaVolume[0]) && strategy.smaVolume[0] > 0 ? (strategy.Volume[0] / strategy.smaVolume[0]).ToString("F2") : "NaN")}," +
                                                 $"{realizedPnL:F2},{unrealizedPnL:F2},{tradeDuration}," +
                                                 $"{stopPrice:F2},{targetPrice:F2},{strategy.totalCommission:F2},{reason.Replace(",", ";")},{strategy.fileSetID}");
                    }
                    catch (Exception ex)
                    {
                        strategy.Print($"[Error] Failed to write TradeEvent log: {ex.Message}");
                    }
                }
            }

            public void LogDebug(string level, string category, double value1, double value2, string message)
            {
                if (!strategy.EnableLogging || !strategy.DebugMode || debugLogWriter == null) return;
                bool isCritical = criticalTags.Any(tag => message.Contains(tag));
                if (!isCritical && strategy.CurrentBar - lastDebugBar < DebugThrottleBars) return;
                lock (strategy.orderLock)
                {
                    debugLogWriter.WriteLine($"{strategy.Time[0]:yyyy-MM-dd HH:mm:ss},{strategy.CurrentBar},{level},{category},{value1:F2},{value2:F2},{message}");
                    if (!isCritical) lastDebugBar = strategy.CurrentBar;
                }
            }

            public void Log(string message, string level, string category = "General", double value1 = 0, double value2 = 0)
            {
                if (!strategy.EnableLogging || (level == "Debug" && !strategy.DebugMode) ||
                    (debugLogWriter == null && level == "Debug") ||
                    (tradeLogWriter == null && level != "Debug")) return;

                // ✅ DIFF 713 v2: Safe timestamp generation to prevent null reference crashes
                string timestamp;
                if (strategy.Bars != null && strategy.Bars.Count > 0 && strategy.Times != null && strategy.Times[0].Count > 0)
                    timestamp = strategy.Time[0].ToString("yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                else
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);

                bool isCritical = criticalTags.Any(tag => category.IndexOf(tag, StringComparison.OrdinalIgnoreCase) >= 0 || message.IndexOf(tag, StringComparison.OrdinalIgnoreCase) >= 0);

                lock (strategy.orderLock)
                {
                    if (debugLogWriter != null && level == "Debug")
                    {
                        if (isCritical || strategy.CurrentBar - lastDebugBar >= DebugThrottleBars)
                        {
                            debugLogWriter.WriteLine($"{timestamp},{strategy.CurrentBar},{level},{category},{value1:F2},{value2:F2},{message}");
                            if (!isCritical)
                                lastDebugBar = strategy.CurrentBar;
                        }
                    }
                    else if (tradeLogWriter != null)
                    {
                        tradeLogWriter.WriteLine($"{timestamp},{strategy.CurrentBar},{level},{category},{value1:F2},{value2:F2},{message}");
                    }
                }
            }

            public void AppendToTradeLog(string message)
            {
                // ✅ ALWAYS append to trade log regardless of EnableLogging flag
                if (tradeLogWriter == null) return;

                lock (strategy.orderLock)
                {
                    tradeLogWriter.WriteLine(message);
                }
            }

            public void LogTrade(string message) => Log($"[Trade] {message}", "Info");
            public void LogEntry(string message) => Log($"[Entry] {message}", "Info");
            public void LogExit(string message) => Log($"[Exit] {message}", "Info");
            public void LogError(string message) => Log($"[Error] {message}", "Error");
            public void LogCritical(string message) => Log($"[Critical] {message}", "Critical");

            public void Close()
            {
                lock (strategy.orderLock)
                {
                    try
                    {
                        if (tradeLogWriter != null)
                        {
                            tradeLogWriter.Close();
                            tradeLogWriter.Dispose();
                            tradeLogWriter = null;
                        }
                        if (debugLogWriter != null)
                        {
                            debugLogWriter.Close();
                            debugLogWriter.Dispose();
                            debugLogWriter = null;
                        }
                    }
                    catch (Exception ex)
                    {
                        strategy.Print($"[Warning] Log close failed: {ex.Message}");
                    }
                }
            }
        }

        internal class StatsTracker
        {
            private readonly EnhancedAnchoredVWAPStrategy strategy;
            public double DailyPnL { get; private set; }
            public double DailyMaxDrawdown { get; private set; }
            public double CumulativeMaxDrawdown { get; private set; } // Add cumulative tracking
            private double dailyHighestEquity;
            private double currentEquity;

            public StatsTracker(EnhancedAnchoredVWAPStrategy strategy)
            {
                this.strategy = strategy;
                ResetDailyStats();
            }

            // Don't reset CumulativeMaxDrawdown in ResetDailyStats
            public void ResetDailyStats()
            {
                DailyPnL = 0;
                DailyMaxDrawdown = 0;
                dailyHighestEquity = 0;
                currentEquity = 0;
                // Note: CumulativeMaxDrawdown is NOT reset here
                if (strategy.DebugMode)
                    strategy.logManager?.Log($"[Debug][StatsReset] DailyPnL:{DailyPnL:F2}, DailyMaxDD:{DailyMaxDrawdown:F2}, CumulativeDD:{CumulativeMaxDrawdown:F2}", "Debug");
            }

            public void UpdateStats(double tradePnL)
            {
                DailyPnL += tradePnL;
                dailyHighestEquity = Math.Max(dailyHighestEquity, DailyPnL);
                currentEquity = DailyPnL;
                DailyMaxDrawdown = Math.Max(DailyMaxDrawdown, dailyHighestEquity - currentEquity);

                // Update cumulative drawdown using NT8's calculation if available
                if (strategy.SystemPerformance != null && strategy.SystemPerformance.AllTrades.Count > 0)
                {
                    CumulativeMaxDrawdown = strategy.SystemPerformance.AllTrades.TradesPerformance.Currency.Drawdown;
                }
            }

            public void UpdateEquity(double unrealizedPnL)
            {
                currentEquity = DailyPnL + unrealizedPnL;
                dailyHighestEquity = Math.Max(dailyHighestEquity, currentEquity);
                DailyMaxDrawdown = Math.Max(DailyMaxDrawdown, dailyHighestEquity - currentEquity);

                // NOTE: We no longer update CumulativeMaxDrawdown here based on closed trades
                // The intra-trade cumulative drawdown is now calculated and checked in ShouldContinueProcessing

                if (strategy.DebugMode && strategy.CurrentBar % 60 == 0) // Every hour
                {
                    // Get the intra-trade cumulative drawdown from ShouldContinueProcessing - This is no longer available
                    // double currentTotalEquity = (strategy.SystemPerformance?.AllTrades?.TradesPerformance?.Currency?.CumProfit ?? 0) + unrealizedPnL;
                    // double intraTradeCumulativeDrawdown = strategy.overallPeakEquity - currentTotalEquity; // Removed overallPeakEquity

                    // UPDATED Log message to remove cumulative drawdown
                    strategy.logManager?.Log($"[Debug][EquityCurve] Bar:{strategy.CurrentBar} Equity:{currentEquity:F2}, DailyDD:{DailyMaxDrawdown:F2}", "Debug");
                }
                else if (strategy.DebugMode && strategy.CurrentBar % 10 == 0)
                    strategy.logManager?.Log($"[Debug][Equity] Bar:{strategy.CurrentBar} Realized:{DailyPnL:F2}, Unrealized:{unrealizedPnL:F2}, Current:{currentEquity:F2}, MaxDD:{DailyMaxDrawdown:F2}", "Debug");
            }
        }
        #endregion

        #region Event Handlers
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string nativeError)
        {
            if (order == null)
                return;

            // ✅ DIFF 68: Capture native stop order reference for later cancellation
            if (order.Name.Contains("_Stop") && order.OrderType == OrderType.StopMarket && orderState == OrderState.Working)
            {
                nativeStopLoss = order;
                Print($"[CaptureNativeStop] Captured stop order: {order.Name} for later cancellation");
            }

            // Log rejected bracket orders
            if (order.OrderState == OrderState.Rejected &&
                (order.Name.Contains("_Stop") || order.Name.Contains("_Target")))
            {
                if (logManager != null)
                {
                    string errorDetails = string.IsNullOrEmpty(nativeError) ? error.ToString() : nativeError;
                    logManager.Log($"[Critical] Bracket order rejected: {order.Name}, Reason: {errorDetails}", "Critical");
                    Print($"[Critical] Bracket order rejected: {order.Name}, Reason: {errorDetails}");

                    // Handle rejected bracket order
                    HandleRejectedBracketOrder(order, errorDetails);
                }
            }

            // Debug logging for all bracket order updates
            if (DebugMode && (order.Name.Contains("_Stop") || order.Name.Contains("_Target")))
            {
                if (logManager != null)
                    logManager.Log($"[Debug][OrderUpdate] {order.Name} → {orderState} at {time:HH:mm:ss}", "Debug");
            }
        }

        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            base.OnExecutionUpdate(execution, executionId, price, quantity, marketPosition, orderId, time);

            if (execution == null || execution.Order == null)
                return;

            // ✅ DIFF 51: CLEANUP - Remove all MFE trail arming logic from OnExecutionUpdate (now handled solely in BIP 1)
            // This logic was causing post-exit trail activation with stale state

            // ✅ DIFF 14: Snapshot the trade ID at the moment of this fill in case it changes after reset
            string tradeIdAtExit = currentTradeID;

            // 🔍 DEBUG: Log ALL executions to track fill pipeline
            Print($"[EXEC_DEBUG] Order: {execution.Order.Name} | State: {execution.Order.OrderState} | Action: {execution.Order.OrderAction} | Qty: {quantity} | Price: {price:F2} | PosAfter: {Position.MarketPosition} | Time: {execution.Time:HH:mm:ss}");

            // ✅ Set TradeID ONCE on any entry fill (primary logic)
            if (execution.Order.OrderState == OrderState.Filled &&
                (execution.Order.Name?.EndsWith("_1") ?? false) &&
                Position.MarketPosition != MarketPosition.Flat)
            {
                if (string.IsNullOrEmpty(currentTradeID))
                {
                    currentTradeID = Guid.NewGuid().ToString();
                    Print($"[GUID SET] TradeID = {currentTradeID} | Signal = {execution.Order.Name} @ {execution.Time:HH:mm:ss}");
                }
                else
                {
                    Print($"[GUID SKIP] TradeID already exists: {currentTradeID} | Signal: {execution.Order.Name}");
                }
            }
            else if (execution.Order.OrderState == OrderState.Filled && (execution.Order.Name?.EndsWith("_1") ?? false))
            {
                Print($"[GUID BLOCK] Entry fill blocked - Position: {Position.MarketPosition} | TradeID: {currentTradeID} | Signal: {execution.Order.Name}");
            }

            // ✅ DIFF 709: Ensure TradeID is always set immediately after fill
            if (execution.Order.OrderState == OrderState.Filled &&
                Position.MarketPosition != MarketPosition.Flat)
            {
                if (string.IsNullOrWhiteSpace(currentTradeID) || currentTradeID == "Unknown")
                {
                    currentTradeID = Guid.NewGuid().ToString();
                    Print($"[GUID SET] TradeID = {currentTradeID} | Signal = {execution.Order.Name} | Time = {execution.Time:HH:mm:ss}");
                }
            }

            // ✅ DIFF 700: Set lastExitReason on exit fills
            if (execution.Order.OrderState == OrderState.Filled &&
                (execution.Order.Name?.Contains("Target") ?? false))
            {
                lastExitReason = "ProfitTarget";
                Print($"[Reason Set] Exit reason set to ProfitTarget @ {execution.Time:HH:mm:ss}");
            }
            else if (execution.Order.OrderState == OrderState.Filled &&
                     (execution.Order.Name?.Contains("Stop") ?? false))
            {
                lastExitReason = "StopLoss";
                Print($"[Reason Set] Exit reason set to StopLoss @ {execution.Time:HH:mm:ss}");
            }

            // Add redundant logging to verify entry fill and signal consistency
            if (DebugMode && logManager != null && execution != null && execution.Order != null)
            {
                logManager.Log($"[Debug][ExecutionUpdate] Name={execution.Order.Name}, Signal={currentSignalName}, Qty={quantity}, State={execution.Order.OrderState}, PosQty={Position.Quantity}", "Debug");
            }

            lock (orderLock)
            {
                allExecutions.Add(execution);

                // Track commission
                if (execution.Commission > 0)
                {
                    totalCommission += execution.Commission;
                    if (DebugMode)
                        logManager?.Log($"[Debug][Commission] Execution {executionId}: ${execution.Commission:F2}, Total: ${totalCommission:F2}", "Debug");
                }

                // Process execution in TradeManager
                tradeManager?.OnExecutionUpdate(execution, price);

                // Reset MFE trail state on any execution fill
                if (execution.Order != null && execution.Order.OrderState == OrderState.Filled)
                {
                    // ✅ DIFF 9: General entry fill handling with race condition guard
                    if ((execution.Order.OrderAction == OrderAction.Buy || execution.Order.OrderAction == OrderAction.SellShort) &&
                        Position.MarketPosition != MarketPosition.Flat)
                    {
                        string filledSignalName = execution.Order.Name;

                        // ✅ DIFF 30: Track entry bar index to prevent immediate stopouts
                        entryBarIndex = CurrentBar;
                        entryPrice = execution.Price;

                        // ✅ DIFF 41: Cache entry data for reliable MFE trail tracking
                        cachedEntryPrice = execution.Price;
                        cachedEntryTime = execution.Time;
                        isTradeActive = true;
                        entryCapturedFallback = false;

                        Print($"[ENTRY_BAR_GUARD] Entry filled at bar {entryBarIndex} - exit logic blocked for 2 bars");

                        // ✅ DIFF 80: Implement optimal $800 hard stop
                        double hardStopPrice = Position.MarketPosition == MarketPosition.Long
                            ? entryPrice - hardStopDist
                            : entryPrice + hardStopDist;

                        if (Position.MarketPosition == MarketPosition.Long)
                            ExitLongStopMarket(0, true, Position.Quantity, hardStopPrice, "HardStop_800", filledSignalName);
                        else if (Position.MarketPosition == MarketPosition.Short)
                            ExitShortStopMarket(0, true, Position.Quantity, hardStopPrice, "HardStop_800", filledSignalName);

                        Print(string.Format("[✅ HARD STOP SET] {0} | Entry: {1:F2} | StopPx: {2:F2} | Risk: ${3:F0}",
                            filledSignalName, entryPrice, hardStopPrice, hardStopDist * 5.0));

                        // --- RACE CONDITION GUARD FOR ENTRY ---
                        if (!string.IsNullOrEmpty(currentTradeID))
                        {
                            if (tradeIdToSignalMap.ContainsKey(currentTradeID))
                            {
                                Print(string.Format("[GUID SKIP] TradeID already exists: {0} | Signal: {1}", currentTradeID, tradeIdToSignalMap[currentTradeID]));
                            }

                            Print(string.Format("{0:HH:mm:ss} [WARN_TRADEID_RACE] currentTradeID still set to {1} during new entry fill for {2}",
                                time, currentTradeID, filledSignalName));

                            // Generate a fresh GUID to prevent collision
                            currentTradeID = Guid.NewGuid().ToString();
                            Print(string.Format("{0:HH:mm:ss} [NEW_TRADEID_RECOVERY] Assigned (recover): {1} for entry: {2}",
                                time, currentTradeID, filledSignalName));
                        }
                        else
                        {
                            currentTradeID = Guid.NewGuid().ToString();
                            Print(string.Format("{0:HH:mm:ss} [NEW_TRADEID] Assigned: {1} for entry: {2}",
                                time, currentTradeID, filledSignalName));
                        }

                        tradeIdToSignalMap[currentTradeID] = filledSignalName;
                        tradeIdToEntryTimeMap[currentTradeID] = execution.Time;
                        tradeIdToMaxPnLMap[currentTradeID] = 0.0; // will be updated in MFE logic
                        Print(string.Format("[TRADE_MAP] Stored signal '{0}' and EntryTime '{1:HH:mm:ss}' for TradeID: {2}", filledSignalName, execution.Time, currentTradeID));
                    }

                    // ✅ Legacy signal-specific handling (kept for compatibility - TradeID already assigned above)
                    if (!string.IsNullOrEmpty(currentSignalName) && execution.Order.Name == currentSignalName)
                    {
                        // TradeID assignment is now handled by the general entry fill handler above
                        // This section is kept for any signal-specific logic that might be needed
                        Print($"[LEGACY_SIGNAL_MATCH] Signal '{currentSignalName}' matched order '{execution.Order.Name}' for TradeID: {currentTradeID}");
                    }

                    // ✅ DIFF 14: Use tradeIdAtExit snapshot for improved MFE deduplication
                    if (Position.MarketPosition == MarketPosition.Flat && !string.IsNullOrEmpty(tradeIdAtExit))
                    {
                        if (!mfeTrailLoggedTradeIds.Contains(tradeIdAtExit))
                        {
                            Trade lastTrade = null;
                            if (SystemPerformance.AllTrades.Count > 0)
                                lastTrade = SystemPerformance.AllTrades.Last();

                            if (lastTrade != null && lastTrade.Entry != null && lastTrade.Exit != null)
                            {
                                double entryPrice = lastTrade.Entry.Price;
                                double exitPrice = lastTrade.Exit.Price;
                                DateTime entryTime = tradeIdToEntryTimeMap.ContainsKey(tradeIdAtExit)
                                    ? tradeIdToEntryTimeMap[tradeIdAtExit]
                                    : lastTrade.Entry.Time;
                                DateTime tradeExitTime = lastTrade.Exit.Time;
                                double finalPnL = lastTrade.ProfitCurrency;

                                // ✅ DIFF 15: Finalize signal name attribution with robust fallback hierarchy
                                string signalNameToUse;
                                if (tradeIdToSignalMap.ContainsKey(tradeIdAtExit))
                                {
                                    signalNameToUse = tradeIdToSignalMap[tradeIdAtExit]; // best match: our explicit mapping
                                }
                                else if (!string.IsNullOrEmpty(lastTrade.Entry.Name))
                                {
                                    signalNameToUse = lastTrade.Entry.Name; // fallback: NT8's Entry.Name
                                }
                                else if (!string.IsNullOrEmpty(currentSignalName))
                                {
                                    signalNameToUse = currentSignalName; // final fallback: current active signal
                                }
                                else
                                {
                                    signalNameToUse = "UnknownSignal"; // safety net
                                }

                                // ✅ DIFF 24: Enhanced reason attribution with comprehensive order name analysis
                                string reasonToUse = "System_Exit_Unspecified";
                                if (execution.Order != null && !string.IsNullOrEmpty(execution.Order.Name))
                                {
                                    string orderName = execution.Order.Name;
                                    if (orderName.Contains("_Target"))
                                        reasonToUse = "ProfitTarget";
                                    else if (orderName.Contains("_Stop"))
                                        reasonToUse = "StopLoss";
                                    else if (orderName == "Exit on session close")
                                        reasonToUse = "SessionEnd";
                                    else if (orderName.Contains("MFE_Trail_Exit"))
                                        reasonToUse = string.IsNullOrEmpty(lastMfeTrailExitReason) ? "MFE_Trail_ExitFill" : lastMfeTrailExitReason;
                                    else if (trailExitSubmitted)
                                        reasonToUse = string.IsNullOrEmpty(lastMfeTrailExitReason) ? "MFE_Intent_OtherFill" : lastMfeTrailExitReason;
                                    else if (!string.IsNullOrEmpty(lastExitReason))
                                        reasonToUse = lastExitReason;
                                }
                                else if (trailExitSubmitted)
                                {
                                    reasonToUse = string.IsNullOrEmpty(lastMfeTrailExitReason) ? "MFE_Intent_OtherFill" : lastMfeTrailExitReason;
                                }
                                else if (!string.IsNullOrEmpty(lastExitReason))
                                {
                                    reasonToUse = lastExitReason;
                                }

                                // ✅ DIFF 23: Refine maxPnlForLog logic to remove unreliable use of tradeManager.MaxFavorableExcursion
                                double maxPnlForLog = 0.0;

                                // Priority 1: MFE tracked specifically for this tradeID
                                if (tradeIdToMaxPnLMap.ContainsKey(tradeIdAtExit))
                                    maxPnlForLog = tradeIdToMaxPnLMap[tradeIdAtExit];

                                // Priority 2: Skip NT8's MFE since Trade object doesn't have Mfe property in NinjaTrader
                                // (This was removed to avoid compilation errors)

                                // Priority 3: Fallback — if PnL was positive, use it as a proxy for max excursion
                                if (maxPnlForLog == 0 && finalPnL > 0)
                                    maxPnlForLog = finalPnL;

                                // ✅ DIFF 17: Finalize override flag, debug print, and logging call
                                // === OVERRIDE FLAG ===
                                // If reason is NOT MFE-related, it's an override of MFE trail
                                bool isOverride = !reasonToUse.StartsWith("MFE_");

                                // === DEBUG ===
                                Print(string.Format("[EXEC_UPDATE_LOG_PREP] TradeID={0}, Signal={1}, Reason={2}, MaxPnL={3:F2}, FinalPnL={4:F2}, Override={5}, trailExitSub={6}, lastMFEExitRsn={7}, lastExitRsn={8}",
                                    tradeIdAtExit,
                                    signalNameToUse,
                                    reasonToUse,
                                    maxPnlForLog,
                                    finalPnL,
                                    isOverride,
                                    trailExitSubmitted,
                                    lastMfeTrailExitReason ?? "null",
                                    lastExitReason ?? "null"));

                                // === WRITE LOG ===
                                WriteMFETrailLogOnce(
                                    tradeIdAtExit,
                                    signalNameToUse,
                                    entryPrice,
                                    exitPrice,
                                    entryTime,
                                    tradeExitTime,
                                    maxPnlForLog,
                                    finalPnL,
                                    reasonToUse,
                                    isOverride
                                );

                                // ✅ DIFF 11: Clean up residual logging state after trade exit
                                Print($"[CLEANUP] Reset logging state after logging tradeID={tradeIdAtExit}");
                                lastMfeTrailExitReason = "";
                                lastExitReason = "";
                                trailExitSubmitted = false;
                                currentTradeID = ""; // Full reset here is safe since we're now flat and logged
                            }
                            else
                            {
                                // ✅ DIFF 21: Add fallback [WARN_LOG_SKIP] if trade details are null
                                string reason = string.IsNullOrEmpty(lastExitReason) ? "UnknownExit" : lastExitReason;
                                string signalName = tradeIdToSignalMap.ContainsKey(tradeIdAtExit) ? tradeIdToSignalMap[tradeIdAtExit] : currentSignalName;
                                Print(string.Format("[WARN_LOG_SKIP] Trade not logged. TradeID={0}, Signal={1}, Reason={2}, trailExitSub={3}, lastMFEExitRsn={4}",
                                    tradeIdAtExit,
                                    string.IsNullOrEmpty(signalName) ? "UnknownSignal" : signalName,
                                    reason,
                                    trailExitSubmitted,
                                    lastMfeTrailExitReason ?? "null"));

                                // ✅ DIFF 12: Add safety guard against lastTrade.Entry == null edge case
                                Print(string.Format("[LOG_ERROR] lastTrade or its Entry/Exit fields are null. TradeID={0}, SystemPerformance Count={1}",
                                    tradeIdAtExit, SystemPerformance.AllTrades.Count));
                                if (lastTrade != null)
                                    Print(string.Format("[LOG_ERROR] lastTrade exists but Entry={0}, Exit={1}",
                                        lastTrade.Entry == null ? "NULL" : "OK",
                                        lastTrade.Exit == null ? "NULL" : "OK"));
                            }
                        }
                        else
                        {
                            Print($"[TRAIL_LOG_SKIP] TradeID already written: {tradeIdAtExit}");
                        }
                    }

                    // --- MFE Trail Reset Logic ---
                    mfeTrailArmed = false;
                    mfeTrailLogArmedPrinted = false;
                    highestUnrealizedPnL = 0;
                    mfeTrailMaxPnL = 0;
                    mfeTrailArmedTime = Core.Globals.MinDate;
                    mfeTrailArmedPrice = 0;
                    lastLoggedManualPnL = double.MinValue;
                    lastLoggedClose = double.MinValue;
                    mfeExitSubmitted = false;

                    // Reset 3-phase MFE trail model variables
                    initialStopPrice = 0.0;
                    breakevenPrice = 0.0;
                    trailingStopPrice = 0.0;
                    breakevenStopActive = false;
                    atrTrailActive = false;

                    if (tradeManager != null)
                        tradeManager.exitLogged = false;

                    Print($"[Reset] MFE Trail state reset on fill: OrderId={execution.Order.OrderId}, Action={execution.Order.OrderAction}, Time={time}");

                    // Track partial fills for our entry order
                    if (!string.IsNullOrEmpty(currentSignalName) &&
                        execution.Order.Name == currentSignalName)
                    {
                        // Update cumulative filled quantity
                        totalFilledQuantity += execution.Quantity;
                        if (DebugMode && logManager != null)
                            logManager.Log($"[Debug][ExecutionUpdate] {execution.Quantity} filled for {execution.Order.Name}. Cumulative={totalFilledQuantity}/{intendedEntrySize}", "Debug");

                        // Only place bracket orders when total filled quantity equals intended entry size
                        if (totalFilledQuantity >= intendedEntrySize && !exitOrdersPlaced && Position.Quantity > 0)
                        {
                            // Add detailed logging for entry fill verification
                            if (DebugMode && logManager != null)
                                logManager.Log($"[Debug][EntryFilled] Signal {currentSignalName} fully filled. Calling ManageOpenPosition. Qty={Position.Quantity}, EntryPrice={tradeManager.EntryPrice:F2}", "Debug");

                            entrySubmitted = false; // ✅ Reset lock ONLY after full fill
                            // Place bracket orders after entry fill
                            ManageOpenPositionWithBracket(currentSignalName, Position.Quantity, tradeManager.EntryPrice);
                            exitOrdersPlaced = true;
                            Print($"[Bracket] Placed bracket orders for {currentSignalName} at {time}");
                            if (DebugMode && logManager != null)
                            {
                                logManager.Log($"[Debug][Bracket] exitOrdersPlaced set to true for {currentSignalName}", "Debug");
                                logManager.Log($"[Debug][Bracket] All {intendedEntrySize} filled — placing bracket orders.", "Debug");
                            }
                        }
                    }
                    // 🧯 Edge Case Fix: If trade instantly exits, force reset for next entries
                    else if (execution.Order.OrderState == OrderState.Filled &&
                        execution.Order.Name.Contains("_Stop") &&
                        Position.MarketPosition == MarketPosition.Flat &&
                        execution.Time.Subtract(execution.Order.Time).TotalSeconds < 2)
                    {
                        entrySubmitted = false;
                        exitOrdersPlaced = false;
                        if (tradeManager != null) tradeManager.exitLogged = false;
                        mfeExitSubmitted = false;
                        mfeTrailArmed = false;
                        breakevenStopActive = false;
                        atrTrailActive = false; // Added to ensure all MFE phase flags are reset
                        if (DebugMode && logManager != null)
                            logManager?.Log("[Recovery] Resetting entry/exit/MFE flags after near-instant stop", "Debug");
                    }
                    else if (execution.Order.OrderAction == OrderAction.Buy || execution.Order.OrderAction == OrderAction.SellShort)
                    {
                        // Log when entry order fills but doesn't match our criteria
                        if (DebugMode && logManager != null)
                        {
                            string reason = string.IsNullOrEmpty(currentSignalName) ? "currentSignalName is null/empty" :
                                           execution.Order.Name != currentSignalName ? $"Order name '{execution.Order.Name}' doesn't match signal '{currentSignalName}'" :
                                           exitOrdersPlaced ? "exitOrdersPlaced already true" :
                                           Position.Quantity <= 0 ? "Position.Quantity <= 0" : "Unknown reason";

                            logManager.Log($"[Warning][EntryFill] Entry order filled but bracket orders NOT placed. Reason: {reason}", "Warning");
                        }
                    }

                    // After entry fill, call ManageOpenPosition to set up initial tracking
                    if ((execution.Order.Name?.EndsWith("_1") ?? false) && execution.Order.OrderState == OrderState.Filled)
                    {
                        ManageOpenPosition(execution.Order, price, time);
                    }
                    // Log exit order fills for debugging
                    else if (execution.Order.OrderAction == OrderAction.Sell || execution.Order.OrderAction == OrderAction.BuyToCover)
                    {
                        // ✅ DIFF 41: Reset trade active flag on exit fills
                        isTradeActive = false;

                        // ✅ DIFF 51: Enhanced fallback trail logging with NT8 MFE recovery
                        if (Position.MarketPosition == MarketPosition.Flat && mfeTrailShouldHaveLogged && !string.IsNullOrEmpty(currentTradeID))
                        {
                            if (!mfeTrailLoggedTradeIds.Contains(currentTradeID))
                            {
                                Print($"[FALLBACK_TRAIL_LOG] ⚠️ Trade {currentTradeID} should have logged but didn't - writing fallback entry");

                                // Use stored execution time for accurate entry timestamp
                                DateTime entryTime = tradeIdToEntryTimeMap.ContainsKey(currentTradeID)
                                    ? tradeIdToEntryTimeMap[currentTradeID]
                                    : execution.Time;

                                // ✅ DIFF 51: Robust fallback MaxPnL sourcing using multiple sources
                                double maxPnlForLog = 0.0;

                                // First priority: Our internal tracking map
                                if (tradeIdToMaxPnLMap.ContainsKey(currentTradeID))
                                {
                                    maxPnlForLog = tradeIdToMaxPnLMap[currentTradeID];
                                    Print($"[MFE_RECOVERY_INTERNAL] Using internal tracking: {maxPnlForLog:F2} for TradeID {currentTradeID}");
                                }

                                // Second priority: Use tradeManager MFE if available
                                if (maxPnlForLog == 0 && tradeManager?.MaxFavorableExcursion > 0)
                                {
                                    maxPnlForLog = tradeManager.MaxFavorableExcursion;
                                    Print($"[MFE_RECOVERY_MANAGER] Using tradeManager MFE: {maxPnlForLog:F2} for TradeID {currentTradeID}");
                                }

                                // Third priority: Use final PnL if no other MFE available
                                Trade lastTrade = SystemPerformance.AllTrades.Count > 0 ? SystemPerformance.AllTrades.Last() : null;
                                if (maxPnlForLog == 0 && lastTrade != null && lastTrade.ProfitCurrency > 0)
                                {
                                    maxPnlForLog = lastTrade.ProfitCurrency;
                                    Print($"[MFE_RECOVERY_FINALPNL] Used FinalPnL: {maxPnlForLog:F2} for TradeID {currentTradeID}");
                                }

                                double maxMFE = maxPnlForLog;

                                WriteMFETrailLogOnce(
                                    currentTradeID,
                                    currentSignalName,
                                    Position.AveragePrice,
                                    execution.Price,
                                    entryTime,
                                    execution.Time,
                                    maxMFE,
                                    lastTrade?.ProfitCurrency ?? 0, // Use actual final PnL
                                    lastMfeTrailExitReason.IsNullOrEmpty() ? "FallbackExit" : lastMfeTrailExitReason,
                                    true); // wasOverride = true for fallback

                                // ✅ DIFF 51: Mark as logged and reset trail state cleanly
                                mfeTrailLoggedTradeIds.Add(currentTradeID);
                                mfeTrailShouldHaveLogged = false;

                                // Reset all trail state cleanly
                                lastMfeTrailExitReason = "";
                                trailExitSubmitted = false;
                                Print($"[TRAIL_STATE_RESET] Cleaned up trail state for TradeID: {currentTradeID}");
                            }
                        }

                        if (DebugMode && logManager != null)
                        {
                            string exitType = execution.Order.Name.Contains("_Target") ? "Target" :
                                             execution.Order.Name.Contains("_Stop") ? "Stop" :
                                             execution.Order.Name.Contains("MFE_Trail") ? "MFE_Trail" : "Other";

                            logManager.Log($"[Debug][ExitFill] {exitType} exit filled. Order={execution.Order.Name}, Price={price:F2}, Qty={quantity}", "Debug");
                        }
                    }
                }

                // We'll handle trade results in OnPositionUpdate instead for more accuracy
            }
        }

        private void ManageOpenPosition(Order entryOrder, double fillPrice, DateTime fillTime)
        {
            if (Position.MarketPosition == MarketPosition.Flat) return;

            // Store entry information for MFE trail tracking
            if (tradeManager != null)
            {
                tradeManager.EntryPrice = fillPrice;
                tradeManager.EntryTime = fillTime;

                // ✅ DIFF 80: Calculate optimal $800 hard stop
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    initialStopPrice = fillPrice - hardStopDist;
                    Print($"[ManageOpenPosition] Long position opened at {fillPrice:F2}, HARD STOP at {initialStopPrice:F2} (${hardStopDist * 5:F0} max risk)");
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    initialStopPrice = fillPrice + hardStopDist;
                    Print($"[ManageOpenPosition] Short position opened at {fillPrice:F2}, HARD STOP at {initialStopPrice:F2} (${hardStopDist * 5:F0} max risk)");
                }

                // Log entry for tracking
                logManager?.Log($"[Entry] Position opened: {Position.MarketPosition}, Price={fillPrice:F2}, Size={Position.Quantity}, InitialStop={initialStopPrice:F2}", "Info");
            }
        }

        private void ManageOpenPositionWithBracket(string signalName, int quantity, double entryPrice)
        {
            // ✅ CRITICAL FIX: Use backsolved stop distance for true risk management
            double atr = Math.Max(atr14[0], minATR);

            double targetPrice = Position.MarketPosition == MarketPosition.Long
                ? entryPrice + atr * ProfitTargetATR
                : entryPrice - atr * ProfitTargetATR;

            // ✅ CRITICAL FIX: Use backsolved currentStopDistance instead of hardStopDist
            double actualStopDistance = currentStopDistance > 0 ? currentStopDistance : GetFallbackStopDistance();
            double stopPrice = Position.MarketPosition == MarketPosition.Long
                ? entryPrice - actualStopDistance
                : entryPrice + actualStopDistance;

            // Calculate actual risk for verification
            double actualRisk = actualStopDistance * quantity * instrumentPointValue;

            if (DebugMode && logManager != null)
                logManager.Log($"[ManualOrder] Submitting bracket for {signalName}: Target={targetPrice:F2}, BacksolvedStop={stopPrice:F2} (${actualRisk:F0} max risk)", "Trade");

            // ✅ CRITICAL FIX: Enhanced stop verification with backsolved values
            Print($"[✅ BACKSOLVED STOP VERIFICATION] {signalName} | Entry: {entryPrice:F2} | StopPx: {stopPrice:F2} | Risk: ${actualRisk:F0} | Distance: {actualStopDistance:F2} points | MaxRisk: ${MaxRiskPerTrade:F0}");

            if (Position.MarketPosition == MarketPosition.Long)
            {
                ExitLongLimit(0, true, quantity, targetPrice, $"{signalName}_Target", signalName);
                ExitLongStopMarket(0, true, quantity, stopPrice, $"{signalName}_Stop", signalName);
                if (DebugMode && logManager != null)
                    logManager.Log($"[SubmitExit][Long] Signal={signalName}, Qty={quantity}, Target={targetPrice:F2}, BacksolvedStop={stopPrice:F2}, Risk=${actualRisk:F0}", "Trade");
                Print($"[BRACKET_SUBMITTED] Long bracket: Target={targetPrice:F2} | Stop={stopPrice:F2} | Risk=${actualRisk:F0}");
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                ExitShortLimit(0, true, quantity, targetPrice, $"{signalName}_Target", signalName);
                ExitShortStopMarket(0, true, quantity, stopPrice, $"{signalName}_Stop", signalName);
                if (DebugMode && logManager != null)
                    logManager.Log($"[SubmitExit][Short] Signal={signalName}, Qty={quantity}, Target={targetPrice:F2}, BacksolvedStop={stopPrice:F2}, Risk=${actualRisk:F0}", "Trade");
                Print($"[BRACKET_SUBMITTED] Short bracket: Target={targetPrice:F2} | Stop={stopPrice:F2} | Risk=${actualRisk:F0}");
            }
        }

        private void HandleRejectedBracketOrder(Order order, string errorDetails)
        {
            // Extract signal name from the order name (e.g., "Signal_1_Target" -> "Signal_1")
            string signalName = string.Empty;
            if (order.Name.Contains("_Target") || order.Name.Contains("_Stop"))
            {
                int underscoreIndex = order.Name.LastIndexOf('_');
                if (underscoreIndex > 0)
                {
                    signalName = order.Name.Substring(0, underscoreIndex);
                }
            }

            // Log detailed information about the rejected order
            logManager?.Log($"[Critical][RejectedOrder] Order={order.Name}, Signal={signalName}, Action={order.OrderAction}, Type={order.OrderType}, Error={errorDetails}", "Critical");

            // Check if both target and stop are rejected (serious issue)
            bool isStopRejected = Orders.Any(o => o.Name.Contains("_Stop") && o.OrderState == OrderState.Rejected);
            bool isTargetRejected = Orders.Any(o => o.Name.Contains("_Target") && o.OrderState == OrderState.Rejected);

            // If both bracket legs are rejected, consider a manual exit
            if (isStopRejected && isTargetRejected && Position.MarketPosition != MarketPosition.Flat)
            {
                logManager?.Log($"[Critical][SafetyExit] Both stop and target rejected. Initiating safety exit for {Position.MarketPosition} position.", "Critical");

                // Submit a market order to exit the position
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    ExitLong("BracketRejection_SafetyExit");
                }
                else if (Position.MarketPosition == MarketPosition.Short)
                {
                    ExitShort("BracketRejection_SafetyExit");
                }
            }
            // If only the stop is rejected (risk management issue)
            else if (isStopRejected && Position.MarketPosition != MarketPosition.Flat)
            {
                logManager?.Log($"[Critical][StopRejected] Stop order rejected. Consider manual risk management for {Position.MarketPosition} position.", "Critical");

                // Attempt to resubmit the stop order with a slightly different price
                RetryRejectedStopOrder(signalName);
            }
            // If only the target is rejected (profit management issue)
            else if (isTargetRejected && Position.MarketPosition != MarketPosition.Flat)
            {
                logManager?.Log($"[Warning][TargetRejected] Target order rejected. MFE trail will still manage exits.", "Warning");

                // We can continue without a target since MFE trail will handle exits
                // Optionally retry the target order if needed
            }
        }

        private void RetryRejectedStopOrder(string signalName)
        {
            if (Position.MarketPosition == MarketPosition.Flat)
                return;

            // ✅ DIFF 80: Calculate hard stop for retry
            double entryPrice = tradeManager.EntryPrice;
            double originalStopPrice = Position.MarketPosition == MarketPosition.Long
                ? entryPrice - hardStopDist
                : entryPrice + hardStopDist;

            // Adjust the stop price by 1-2 ticks to avoid rejection for the same reason
            double adjustedStopPrice = Position.MarketPosition == MarketPosition.Long
                ? originalStopPrice - (TickSize * 2)
                : originalStopPrice + (TickSize * 2);

            logManager?.Log($"[Recovery] Retrying rejected stop order with adjusted price. Original={originalStopPrice:F2}, New={adjustedStopPrice:F2}", "Warning");

            // Submit a new stop order with the adjusted price
            string retryOrderName = $"{signalName}_Stop_Retry";
            if (Position.MarketPosition == MarketPosition.Long)
            {
                ExitLongStopMarket(0, true, Position.Quantity, adjustedStopPrice, retryOrderName, signalName);
            }
            else if (Position.MarketPosition == MarketPosition.Short)
            {
                ExitShortStopMarket(0, true, Position.Quantity, adjustedStopPrice, retryOrderName, signalName);
            }
        }

        // Added to handle position updates and log final trade results
        protected override void OnPositionUpdate(Position position, double averagePrice, int quantity, MarketPosition marketPosition)
        {
            base.OnPositionUpdate(position, averagePrice, quantity, marketPosition);

            // When position goes flat and we have completed trades
            if (marketPosition == MarketPosition.Flat && quantity == 0)
            {
                // ✅ DIFF 53: Reset trail state when position goes flat - DELAYED to allow logging
                // Note: State reset moved to end of OnPositionUpdate to allow proper logging
                Print($"[OnPositionUpdate] Position flat detected - will reset state after logging");

                // ✅ DIFF 44: Check for missing trail logs before reset
                if (!string.IsNullOrEmpty(currentTradeID) && mfeTrailShouldHaveLogged && !mfeTrailLoggedTradeIds.Contains(currentTradeID))
                {
                    Print($"[WARNING] ❌ MFE Trail log missing for TradeID {currentTradeID} - trade should have been logged but wasn't");
                }

                // ✅ DIFF 6: Trade just closed — throttled reset logging to prevent spam
                if (!string.IsNullOrEmpty(currentTradeID) && currentTradeID != lastLoggedResetTradeID)
                {
                    Print($"[Reset] Position flat — clearing currentTradeID: {currentTradeID}");
                    lastLoggedResetTradeID = currentTradeID;
                }

                // ✅ DIFF 5: Immediate reset of trade state to prevent reuse
                string tradeIdForLogging = currentTradeID; // Store for logging before reset
                currentTradeID = string.Empty;
                currentSignalName = string.Empty;
                lastExitReason = string.Empty;
                lastMfeTrailExitReason = string.Empty;
                trailExitSubmitted = false;

                // ✅ DIFF 712: Final fallback guard - prevent overwriting valid MFE trail exits
                if (!string.IsNullOrWhiteSpace(tradeIdForLogging) && mfeTrailLoggedTradeIds.Contains(tradeIdForLogging))
                {
                    Print($"[Skip Fallback] TradeID {tradeIdForLogging} already logged. Skipping fallback reason attribution.");
                    return;
                }

                // ✅ DIFF 711: Full exit diagnostic print to expose truth
                double tradePnL = 0;
                DateTime exitTime = Time[0];
                if (SystemPerformance.AllTrades.Count > 0)
                {
                    var lastTrade = SystemPerformance.AllTrades[SystemPerformance.AllTrades.Count - 1];
                    tradePnL = lastTrade.ProfitCurrency;
                    exitTime = lastTrade.Exit.Time;
                }

                Print($"[EXIT AUDIT 🔍] Signal | PnL: {tradePnL:F2} | Reason: {lastExitReason ?? "null"} | TradeID: {tradeIdForLogging ?? "null"} | MaxRiskPerTrade: {MaxRiskPerTrade} | Time: {exitTime:HH:mm:ss}");

                // ✅ DIFF 38: Enhanced fallback logging with FinalPnL validation and override detection
                if (!string.IsNullOrEmpty(tradeIdForLogging) && !mfeTrailLoggedTradeIds.Contains(tradeIdForLogging))
                {
                    Trade lastTrade = SystemPerformance.AllTrades.Count > 0 ? SystemPerformance.AllTrades.Last() : null;
                    if (lastTrade != null && lastTrade.Entry != null && lastTrade.Exit != null)
                    {
                        double entryPrice = lastTrade.Entry.Price;
                        double exitPrice = lastTrade.Exit.Price;
                        double finalPnL = lastTrade.ProfitCurrency;

                        // ✅ DIFF 2: Use stored execution time for accurate entry timestamp
                        DateTime entryTime = tradeIdToEntryTimeMap.ContainsKey(tradeIdForLogging)
                            ? tradeIdToEntryTimeMap[tradeIdForLogging]
                            : lastTrade.Entry.Time; // fallback if not found

                        DateTime tradeExitTime = lastTrade.Exit.Time;

                        // Robust signal attribution
                        string signalNameToUse = "";
                        if (tradeIdToSignalMap.ContainsKey(tradeIdForLogging))
                            signalNameToUse = tradeIdToSignalMap[tradeIdForLogging];
                        else if (lastTrade?.Entry?.Name != null)
                            signalNameToUse = lastTrade.Entry.Name;

                        // Fallback reason logic
                        string reasonToUse = trailExitSubmitted
                            ? (string.IsNullOrEmpty(lastMfeTrailExitReason) ? "MFE_Trail_Submitted_Unknown" : lastMfeTrailExitReason)
                            : (string.IsNullOrEmpty(lastExitReason) ? "StopLoss/Target_Uncaptured" : lastExitReason);

                        // Recover MFE from best available source
                        double maxPnlForLog = tradeManager?.MaxFavorableExcursion ?? 0;
                        if (tradeIdToMaxPnLMap.ContainsKey(tradeIdForLogging) && tradeIdToMaxPnLMap[tradeIdForLogging] > maxPnlForLog)
                            maxPnlForLog = tradeIdToMaxPnLMap[tradeIdForLogging];
                        if (maxPnlForLog == 0 && lastTrade?.ProfitCurrency > 0)
                        {
                            maxPnlForLog = lastTrade.ProfitCurrency;
                            Print($"[Fallback_MFE_Recovery] Used FinalPnL={lastTrade.ProfitCurrency:F2} as MaxPnLDuringTrade for TradeID={tradeIdForLogging}");
                        }

                        // Override flag = true if MFE trail did not submit an exit
                        bool isOverride = !trailExitSubmitted;

                        Print(string.Format("[FALLBACK MFE DEBUG] TradeID={0}, FinalPnL={1:F2}, MaxPnLUsed={2:F2}, trailExitSubmitted={3}, Reason={4}, SignalName={5}",
                            tradeIdForLogging,
                            finalPnL,
                            maxPnlForLog,
                            trailExitSubmitted,
                            reasonToUse,
                            signalNameToUse));

                        WriteMFETrailLogOnce(
                            tradeIdForLogging,
                            signalNameToUse,
                            entryPrice,
                            exitPrice,
                            entryTime,
                            tradeExitTime,
                            maxPnlForLog,
                            finalPnL,
                            reasonToUse,
                            isOverride);
                    }
                    else
                    {
                        // ✅ DIFF 36: Final fallback guard when trade details are missing
                        CheckFinalTrailLoggingFallback(tradeIdForLogging, "TradeDetails_Missing", tradePnL);

                        // ✅ DIFF 12: Add safety guard against lastTrade.Entry == null edge case in OnPositionUpdate
                        Print(string.Format("[LOG_ERROR] OnPositionUpdate: lastTrade or its Entry/Exit fields are null. TradeID={0}, SystemPerformance Count={1}",
                            tradeIdForLogging, SystemPerformance.AllTrades.Count));
                        if (lastTrade != null)
                            Print(string.Format("[LOG_ERROR] OnPositionUpdate: lastTrade exists but Entry={0}, Exit={1}",
                                lastTrade.Entry == null ? "NULL" : "OK",
                                lastTrade.Exit == null ? "NULL" : "OK"));
                    }
                }

                // Reset flags and counters when position is closed
                exitOrdersPlaced = false;
                totalFilledQuantity = 0;
                intendedEntrySize = 0;

                // ✅ DIFF 5: State already reset above, just clean up additional flags
                sessionCloseTriggered = false; // ✅ DIFF 36: Reset session close flag
                exitSubmitted = false;
                exitLogged = false;

                // ✅ DIFF 50: Don't reset MFE here - let it be preserved for fallback logging
                if (tradeManager != null)
                {
                    Print($"[MFE_POSITION_FLAT] Preserving MFE: {tradeManager.MaxFavorableExcursion:F2} for potential fallback logging");
                    // tradeManager.MaxFavorableExcursion = 0; // REMOVED - preserve for fallback logging
                }

                // ✅ DIFF 32: Clean up trade maps using stored tradeId
                if (!string.IsNullOrEmpty(tradeIdForLogging))
                {
                    tradeIdToSignalMap.Remove(tradeIdForLogging);
                    tradeIdToMaxPnLMap.Remove(tradeIdForLogging);
                    tradeIdToEntryTimeMap.Remove(tradeIdForLogging); // ✅ DIFF 1: Clean EntryTime mapping
                    Print($"[TRADE_MAP] Cleaned up maps for TradeID: {tradeIdForLogging}");
                }

                if (DebugMode && logManager != null)
                {
                    logManager.Log($"[Debug] Position flat — exitOrdersPlaced reset to false", "Debug");
                    logManager.Log($"[Debug] Position flat — totalFilledQuantity and intendedEntrySize reset to 0", "Debug");
                    logManager.Log($"[Debug] Position flat — exit attribution variables reset", "Debug");
                }

                // Old MFE trail logging code removed - now handled by WriteMFETrailLogOnce

                if (SystemPerformance.AllTrades.Count > 0)
                {
                    // Get the last completed trade
                    Trade lastTrade = SystemPerformance.AllTrades.Last();
                    // Use the tradePnL already calculated at the top of the method
                    double commission = lastTrade.Commission;

                    // Update daily/cumulative stats
                    statsTracker?.UpdateStats(tradePnL);

                    // --- Post-Trade Target Hit Analysis ---
                    bool likelyTargetHit = false;
                    if (AnalyzeTargetHits && BarsArray[1] != null && atr14 != null) // Check flag and ensure 1-min series/ATR are ready
                    {
                        bool wasLong = lastTrade.Entry.MarketPosition == MarketPosition.Long;
                        // Recalculate target price based on actual entry and ATR at that time (might differ slightly from initial target)
                        // Note: Accessing ATR value at entry bar might require storing it or recalculating if needed.
                        // Using current ATR as an approximation here.
                        double targetPrice = wasLong
                            ? lastTrade.Entry.Price + (atr14[0] * ProfitTargetATR) // Approximation using current ATR
                            : lastTrade.Entry.Price - (atr14[0] * ProfitTargetATR); // Approximation

                        // Ensure bar indices are valid before looping
                        if (lastTrade.Entry.BarIndex >= 0 && lastTrade.Exit.BarIndex >= lastTrade.Entry.BarIndex && lastTrade.Exit.BarIndex < BarsArray[1].Count)
                        {
                            // Loop through the 1-minute bars during the trade
                            for (int i = lastTrade.Entry.BarIndex; i <= lastTrade.Exit.BarIndex; i++)
                            {
                                try // Add try-catch for safety when accessing bar data
                                {
                                    if (wasLong && BarsArray[1].GetHigh(i) >= targetPrice)
                                    {
                                        likelyTargetHit = true;
                                        break; // Exit loop once hit is detected
                                    }
                                    else if (!wasLong && BarsArray[1].GetLow(i) <= targetPrice)
                                    {
                                        likelyTargetHit = true;
                                        break; // Exit loop
                                    }
                                }
                                catch (Exception ex)
                                {
                                    // Log error if accessing bar data fails, but continue analysis
                                    logManager?.Log($"[Error] OnPositionUpdate Target Analysis Loop: Bar {i}, {ex.Message}", "Error");
                                }
                            }
                        }

                        // If analysis suggests target was hit, but trade wasn't profitable, log a warning
                        if (likelyTargetHit && tradePnL <= 0)
                        {
                            logManager?.Log($"[Warning] Post-Trade Analysis: Target likely hit during trade, but PnL <= 0. Entry={lastTrade.Entry.Price:F2}, Exit={lastTrade.Exit.Price:F2}, Target={targetPrice:F2}", "Warning");
                        }
                    }
                    // --- End Post-Trade Target Hit Analysis ---

                    // ✅ DIFF 35: Final trade console log only for valid trades that were processed
                    if (!string.IsNullOrEmpty(tradeIdForLogging))
                    {
                        string cleanReason = !string.IsNullOrEmpty(lastMfeTrailExitReason) ? lastMfeTrailExitReason : lastExitReason;
                        if (string.IsNullOrEmpty(cleanReason)) cleanReason = "Unknown";
                        string result = tradePnL >= 0 ? "WIN" : "LOSS";
                        Print($"[FINAL_TRADE_LOG] {result} | Reason={cleanReason} | PnL=${tradePnL:F2} | Entry={lastTrade.Entry.Time:HH:mm:ss} | Exit={lastTrade.Exit.Time:HH:mm:ss}");
                    }

                    // Reset the exit log flag for the next potential trade
                    if (tradeManager != null)
                        tradeManager.exitLogged = false;
                }
                else
                {
                    // Position closed but no completed trades (e.g., canceled orders)
                    Print("[Position] Position closed without completed trades");

                    // ✅ DIFF 36: Final fallback guard even when no completed trades
                    if (!string.IsNullOrEmpty(tradeIdForLogging))
                    {
                        CheckFinalTrailLoggingFallback(tradeIdForLogging, "NoCompletedTrades", 0.0);
                    }
                }

                // ✅ FINAL STATE RESET: Reset trail state after all logging is complete
                exitLogged = false;
                mfeTrailArmed = false;
                frozenMFEOnArm = 0.0;
                currentTradeID = "";
                if (tradeManager != null)
                    tradeManager.MaxFavorableExcursion = 0;
                lastMfeTrailExitReason = "";
                trailExitSubmitted = false;
                Print($"[OnPositionUpdate] ✅ Trail state reset complete after logging");
            }
        }
        #endregion

        // CheckForAVWAPViolation method removed - AVWAP violation exit logic removed
        // Note: AVWAP indicator is still used for entries and visualization



        #region Helper Methods
        private string EscapeCsvValue(object value)
        {
            if (value == null) return string.Empty;
            string stringValue;
            if (value is double d) stringValue = d.ToString("G");
            else if (value is decimal dec) stringValue = dec.ToString("G");
            else if (value is DateTime dt) stringValue = dt.ToString("yyyy-MM-dd HH:mm:ss");
            else if (value is bool b) stringValue = b.ToString();
            else if (value is Enum e) stringValue = e.ToString();
            else stringValue = value.ToString();
            if (stringValue.Contains(",") || stringValue.Contains("\"") || stringValue.Contains("\n"))
            {
                stringValue = stringValue.Replace("\"", "\"\"");
                return $"\"{stringValue}\"";
            }
            return stringValue;
        }

        private Order GetStopLossOrder(string signalName)
        {
            foreach (Order o in Orders)
            {
                if (o != null && o.Name == signalName && o.OrderType == OrderType.StopMarket)
                    return o;
            }
            return null;
        }
        #endregion


    }
}



