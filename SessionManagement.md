# ManciniMESStrategy - Session Management Documentation

## Overview
The ManciniMESStrategy implements elegant, ETH/RTH agnostic session management that ensures proper daily limit resets while preserving state during same-session reconnections.

## Core Problem Solved
**Issue**: Daily trade limits should reset at proper trading session boundaries, not calendar boundaries. ETH sessions (6:00 PM - 3:50 PM) span midnight, making calendar-based logic incorrect.

**Solution**: Intelligent session boundary detection that works universally for both ETH and RTH modes.

## Architecture

### **Session Detection Logic**
```csharp
private bool IsNewTradingSession(DateTime previousTime, DateTime currentTime)
{
    if (UseETHTradingHours)
    {
        // ETH: Session starts at 6:00 PM ET
        DateTime prevSessionStart = GetETHSessionStart(previousTime);
        DateTime currSessionStart = GetETHSessionStart(currentTime);
        return prevSessionStart.Date != currSessionStart.Date;
    }
    else
    {
        // RTH: Session follows calendar days
        return previousTime.Date != currentTime.Date;
    }
}
```

### **ETH Session Start Calculation**
```csharp
private DateTime GetETHSessionStart(DateTime timestamp)
{
    // ETH session starts at 6:00 PM (18:00)
    if (timestamp.TimeOfDay >= new TimeSpan(18, 0, 0))
    {
        // After 6 PM - session started today
        return timestamp.Date;
    }
    else
    {
        // Before 6 PM - session started yesterday
        return timestamp.Date.AddDays(-1);
    }
}
```

## Session Boundary Examples

### **ETH Mode Examples**
| Previous Time | Current Time | Session Start (Prev) | Session Start (Curr) | New Session? | Action |
|---------------|--------------|---------------------|---------------------|--------------|---------|
| Sun 8:00 PM   | Mon 10:00 AM | Sunday             | Sunday              | No           | Preserve state |
| Mon 2:00 PM   | Mon 8:00 PM  | Sunday             | Monday              | Yes          | Reset daily limits |
| Fri 10:00 PM  | Mon 8:00 PM  | Friday             | Sunday              | Yes          | Reset daily limits |
| Mon 10:00 AM  | Mon 2:00 PM  | Sunday             | Sunday              | No           | Preserve state |

### **RTH Mode Examples**
| Previous Time | Current Time | New Session? | Action |
|---------------|--------------|--------------|---------|
| Mon 10:00 AM  | Mon 2:00 PM  | No           | Preserve state |
| Mon 2:00 PM   | Tue 10:00 AM | Yes          | Reset daily limits |
| Fri 2:00 PM   | Mon 10:00 AM | Yes          | Reset daily limits |

## Integration Points

### **Daily Reset Trigger**
```csharp
// In HandleSessionManagement()
if (sessionStart == DateTime.MinValue || IsNewTradingSession(sessionStart, Time[0]))
{
    ResetDailyVariables(); // tradesToday = 0, dailyStopHit = false, etc.
}
```

### **State Restoration**
```csharp
// In RestoreStrategyState()
isSameDayState = !IsNewTradingSession(restoredSessionStart, Time[0]);
if (!isSameDayState)
{
    // Ignore cross-session state - start fresh
    return;
}
```

## Operational Scenarios

### **Scenario 1: Same Session Reconnection**
**ETH Example:**
- Sunday 8:00 PM: Take 2 trades (tradesToday = 2, maxTrades = 2)
- Sunday 10:00 PM: Connection lost
- Monday 10:00 AM: Reconnect
- **Result**: Same session detected → State restored → tradesToday = 2 → No more trades allowed ✅

### **Scenario 2: New Session Start**
**ETH Example:**
- Monday 2:00 PM: Previous session had 2 trades
- Monday 8:00 PM: New ETH session starts
- **Result**: New session detected → ResetDailyVariables() → tradesToday = 0 → Can take 2 fresh trades ✅

### **Scenario 3: Weekend Gap**
**ETH Example:**
- Friday 10:00 PM: Take 2 trades in Friday session
- Monday 8:00 PM: New week starts
- **Result**: New session detected → Fresh start → tradesToday = 0 ✅

## Benefits

### **✅ Universal Compatibility**
- **ETH Mode**: Proper midnight boundary handling
- **RTH Mode**: Standard calendar day logic
- **Market Replay**: Works from any historical time period
- **Live Trading**: Seamless reconnection handling

### **✅ Risk Management Integrity**
- **Daily Limits**: Always reset at proper session boundaries
- **Trade Counts**: Preserved during same-session reconnections
- **Daily Stops**: Maintained across disconnections within same session
- **Fresh Starts**: Automatic at new session boundaries

### **✅ Zero Configuration**
- **Automatic Detection**: Based on UseETHTradingHours parameter
- **No Manual Setup**: Works out of the box
- **No Edge Cases**: Handles all scenarios universally

## Technical Implementation

### **Key Methods**
- `IsNewTradingSession()`: Universal session boundary detection
- `GetETHSessionStart()`: ETH session start calculation
- `HandleSessionManagement()`: Daily reset trigger
- `RestoreStrategyState()`: Cross-session state filtering

### **State Variables**
- `sessionStart`: Current session start timestamp
- `tradesToday`: Current session trade count
- `dailyStopHit`: Current session catastrophic stop flag

### **Integration**
- Called from `HandleSessionManagement()` on every bar
- Used in `RestoreStrategyState()` for cross-session filtering
- Automatic based on `UseETHTradingHours` parameter

## Conclusion
The elegant session management system ensures that daily trade limits reset properly at trading session boundaries while preserving critical state during same-session reconnections. The solution is ETH/RTH agnostic, requires zero configuration, and handles all edge cases universally.

**Result**: You will never be blocked by yesterday's trades again, while maintaining perfect risk management integrity.
