// --- START OF COMPLETE FILE AlBrooksPriceAction.cs ---
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using System.IO;
using System.Globalization;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
    // Strategy name reflects refinements
    /// <summary>
    /// AlBrooksPriceAction: Enhanced price action strategy inspired by <PERSON>' "Reading Price Charts Bar by Bar".
    /// Implements context/state tracking, explicit failure setup detection, robust logging, session/time awareness,
    /// and scaffolding for trendline/channel logic and discretionary/manual review.
    /// </summary>
    public class AlBrooksPriceAction : Strategy
    {
        // Reusable helper classes
        private LogManager logManager;
        private TradeManager tradeManager;
        private StatsTracker statsTracker;

        // Indicators
        private EMA ema20;
        private ATR atr14;
        private Swing swing;
        private SMA smaBarRange; // For signal bar size check relative to average

        // State Variables
        private MarketContext currentMarketContext = MarketContext.Undetermined;
        private double lastSwingHigh = double.MinValue;
        private double lastSwingLow = double.MaxValue;
        private int lastSwingHighBar = -1; // Relative barsAgo index
        private int lastSwingLowBar = -1;  // Relative barsAgo index

        // High/Low Count State
        private int barsSinceHigh1 = 999; // Init high to avoid triggering early
        private int barsSinceHigh2 = 999;
        private int barsSinceLow1 = 999;
        private int barsSinceLow2 = 999;
        private bool inPullback = false; // General flag indicating potential pullback phase
        private int barsSincePullbackStart = 0; // How many bars since pullback potentially started

        private bool isBarbWire = false;
        private bool firstBarOfSession = false; // Flag for session start

        // Debugging Visuals
        private System.Windows.Controls.TextBlock contextTextBlock;
        private System.Windows.Controls.TextBlock pullbackTextBlock;

        private const int WARMUP_BARS = 30; // Ensure enough bars for indicators

        #region Strategy Parameters (Includes Enhancements)

        // --- Volatility Filter Parameters ---
        [NinjaScriptProperty]
        [Range(0.0, 100.0)]
        [Display(Name = "Min ATR For Entry (Points)", Description = "Minimum ATR value (in points) required to allow entries", Order = 56, GroupName = "6. Filters")]
        public double MinAtrForEntry { get; set; }

        [NinjaScriptProperty]
        [Range(0.5, 100.0)] // Min should usually be > 0
        [Display(Name = "Max ATR For Entry (Points)", Description = "Maximum ATR value (in points) allowed to allow entries", Order = 57, GroupName = "6. Filters")]
        public double MaxAtrForEntry { get; set; }

        // --- Settings ---
        [NinjaScriptProperty]
        [Display(Name="Enable Logging", Order=1, GroupName="1. Settings")]
        public bool EnableLogging { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Debug Mode", Description="Enables detailed logging and chart visuals", Order=2, GroupName="1. Settings")]
        public bool DebugMode { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Info Logging", Description="Log info-level events", Order=3, GroupName="1. Settings")]
        public bool EnableInfoLogging { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Warning Logging", Description="Log warning-level events", Order=4, GroupName="1. Settings")]
        public bool EnableWarningLogging { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Enable Critical Logging", Description="Log critical-level events", Order=5, GroupName="1. Settings")]
        public bool EnableCriticalLogging { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Manual State Reset", Description = "Manually reset daily state on next bar (for debugging)", Order = 6, GroupName = "1. Settings")]
        public bool ManualReset { get; set; }

        // --- Session/Time Parameters ---
        [NinjaScriptProperty]
        [Range(0, 23)]
        [Display(Name = "Session End Hour", Description = "Hour to end session (local time)", Order = 7, GroupName = "1. Settings")]
        public int SessionEndHour { get; set; }

        [NinjaScriptProperty]
        [Range(0, 59)]
        [Display(Name = "Session End Minute", Description = "Minute to end session", Order = 8, GroupName = "1. Settings")]
        public int SessionEndMinute { get; set; }

        // --- Trailing Stop Parameters ---
        [NinjaScriptProperty]
        [Range(0.1, 5.0)]
        [Display(Name = "Trailing Stop ATR Multiplier", Description = "ATR multiplier for trailing stop", Order = 9, GroupName = "4. Risk")]
        public double TrailingStopAtrMultiplier { get; set; }

        // --- Indicators ---
        [NinjaScriptProperty]
        [Range(1, 100)][Display(Name="EMA Period", Order=10, GroupName="2. Indicators")]
        public int EmaPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, 100)][Display(Name="ATR Period", Order=11, GroupName="2. Indicators")]
        public int AtrPeriod { get; set; }

        [NinjaScriptProperty]
        [Range(1, 100)][Display(Name="Swing Strength", Order=12, GroupName="2. Indicators")]
        public int SwingStrength { get; set; }

        [NinjaScriptProperty]
        [Range(5, 50)] [Display(Name = "Avg Range Period", Description="Period for SMA of Bar Range (Signal Bar Check)", Order = 13, GroupName = "2. Indicators")]
        public int AvgRangePeriod { get; set; }

        // --- Analyzer Settings ---
        [NinjaScriptProperty]
        [Range(0, 10)][Display(Name="Slippage (Ticks)", Order=20, GroupName="3. Analyzer Settings")]
        public int SimulationSlippageTicks { get; set; }

        [NinjaScriptProperty]
        [Range(0, 10)][Display(Name="Commission Per Contract (RT)", Order=21, GroupName="3. Analyzer Settings")]
        public double CommissionPerContractRT { get; set; }

        // --- Risk ---
        [NinjaScriptProperty]
        [Range(100, 10000)][Display(Name="Max Daily Loss ($)", Order=30, GroupName="4. Risk")]
        public double MaxDailyLoss { get; set; }

        [NinjaScriptProperty]
        [Range(50, 5000)][Display(Name="Max Risk Per Trade ($)", Order=31, GroupName="4. Risk")]
        public double MaxRiskPerTrade { get; set; }

        [NinjaScriptProperty]
        [Range(0.5, 5.0)][Display(Name="Stop Loss ATR Multiplier", Order=32, GroupName="4. Risk")]
        public double StopLossAtrMultiplier { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Use Structure Stop", Description="If true, uses nearest Swing H/L for initial stop if further than ATR stop", Order=33, GroupName="4. Risk")]
        public bool UseStructureStop { get; set; }

        [NinjaScriptProperty]
        [Range(0.1, 2.0)] [Display(Name="Structure Stop Buffer (ATR)", Description="Buffer added beyond Swing H/L for Structure Stop", Order=34, GroupName="4. Risk")]
        public double StructureStopAtrBuffer { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Use Structure Trail Stop", Description="If Use Structure Stop enabled, trail stop below/above last Swing point", Order = 35, GroupName = "4. Risk")]
        public bool UseStructureTrailStop { get; set; }

        // --- Targets ---
        [NinjaScriptProperty]
        [Display(Name = "Use Structure Target 1", Description = "If true, uses nearest Swing H/L as Target 1", Order = 40, GroupName = "5. Targets")]
        public bool UseStructureTarget1 { get; set; }

        [NinjaScriptProperty]
        [Range(0.5, 10.0)][Display(Name="Fallback Target 1 (ATR)", Description="Profit target multiplier for first exit if Structure Target not found/used", Order=41, GroupName="5. Targets")]
        public double FallbackTarget1AtrMultiplier { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Use Measured Move Target 2", Description = "If true, uses simple Measured Move projection for Target 2", Order = 42, GroupName = "5. Targets")]
        public bool UseMeasuredMoveTarget2 { get; set; }

        [NinjaScriptProperty]
        [Range(1.0, 20.0)][Display(Name="Fallback Target 2 (ATR)", Description="Profit target multiplier for second exit if Structure/MM Target not found/used", Order=43, GroupName="5. Targets")]
        public double FallbackTarget2AtrMultiplier { get; set; }

        [NinjaScriptProperty]
        [Range(1, 100)][Display(Name="Qty Target 1 (%)", Order=44, GroupName="5. Targets")]
        public int QtyTarget1Percent { get; set; }

        // --- Filters ---
        [NinjaScriptProperty]
        [Range(3, 10)][Display(Name = "Barb Wire Lookback", Order=50, GroupName = "6. Filters")]
        public int BarbWireLookback { get; set; }

        [NinjaScriptProperty]
        [Range(0.1, 2.0)][Display(Name = "Barb Wire Max Range (ATR)", Order=51, GroupName = "6. Filters")]
        public double BarbWireMaxRangeAtr { get; set; }

        [NinjaScriptProperty]
        [Range(1, 50)][Display(Name = "Barb Wire Max Body (%)", Order=52, GroupName = "6. Filters")]
        public int BarbWireMaxBodyPercent { get; set; }

        [NinjaScriptProperty]
        [Range(0.1, 1.0)][Display(Name = "Barb Wire EMA Proximity (ATR)", Order=53, GroupName = "6. Filters")]
        public double BarbWireEmaProximityAtr { get; set; }

        [NinjaScriptProperty]
        [Range(3, 20)] [Display(Name = "EMA Trend Lookback", Description="Bars for EMA slope & price position check", Order = 54, GroupName = "6. Filters")]
        public int EmaTrendLookback { get; set; }

        [NinjaScriptProperty]
        [Range(1, 10)][Display(Name = "Min Bars For Pullback Leg", Description = "Min bars needed counter-trend before H/L count resets", Order = 55, GroupName = "6. Filters")]
        public int MinBarsForPullbackLeg { get; set; }

        // --- Signal Bar Quality Filters ---
        [NinjaScriptProperty]
        [Range(10, 80)]
        [Display(Name = "Signal Bar Min Body %", Order = 60, GroupName = "6. Filters")]
        public int SignalMinBodyPercent { get; set; }

        [NinjaScriptProperty]
        [Range(0, 50)]
        [Display(Name = "Signal Bar Max Opposing Tail %", Order = 61, GroupName = "6. Filters")]
        public int SignalMaxOpposingTailPercent { get; set; }

        [NinjaScriptProperty]
        [Range(50, 100)]
        [Display(Name = "Signal Bar Min Favorable Close %", Order = 62, GroupName = "6. Filters")]
        public int SignalFavorableClosePercent { get; set; }

        [NinjaScriptProperty]
        [Range(0.1, 1.0)]
        [Display(Name = "Signal Bar Min Size (ATR)", Description="Signal bar range must be at least this * ATR", Order = 63, GroupName = "6. Filters")]
        public double SignalMinAtrSize { get; set; }

        [NinjaScriptProperty]
        [Display(Name = "Require Non-Inside Signal Bar", Description="If true, signal bar cannot be an inside bar", Order = 64, GroupName = "6. Filters")]
        public bool RequireNonInsideSignalBar { get; set; }

        #endregion

        #region State Management and Initialization
        // === MULTI-TIMEFRAME SCAFFOLDING ADDED ===
        // Add fields for secondary series indicators if needed
        private ATR atr1Min;
        private EMA ema1Min;
        // Add fields for range/session tracking if needed
        private DateTime rangeStartTime, rangeEndTime, sessionEndTime;
        private bool rangeSet = false;
        private double rangeHigh = double.MinValue, rangeLow = double.MaxValue, rangeSize = 0;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "AlBrooksPriceAction"; // Updated name
                Description = @"Enhanced strategy with better state resets and debug visuals.";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1; EntryHandling = EntryHandling.AllEntries; IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 300; IsFillLimitOnTouch = false; MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard; Slippage = SimulationSlippageTicks; StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc; TraceOrders = false; RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution; BarsRequiredToTrade = WARMUP_BARS; IsInstantiatedOnEachOptimizationIteration = true;

                // Default Parameters
                EnableLogging = true; DebugMode = false; ManualReset = false;
                EmaPeriod = 20; AtrPeriod = 14; SwingStrength = 5; AvgRangePeriod = 20;
                SimulationSlippageTicks = 1; CommissionPerContractRT = 2.44; MaxDailyLoss = 1000; MaxRiskPerTrade = 350;
                StopLossAtrMultiplier = 1.5; UseStructureStop = true; StructureStopAtrBuffer = 0.2; UseStructureTrailStop = true;
                UseStructureTarget1 = true; FallbackTarget1AtrMultiplier = 1.5; UseMeasuredMoveTarget2 = true; FallbackTarget2AtrMultiplier = 3.0; QtyTarget1Percent = 50;
                BarbWireLookback = 5; BarbWireMaxRangeAtr = 0.8; BarbWireMaxBodyPercent = 20; BarbWireEmaProximityAtr = 0.5; EmaTrendLookback = 10; MinBarsForPullbackLeg = 2;
                SignalMinBodyPercent = 45; SignalMaxOpposingTailPercent = 30; SignalFavorableClosePercent = 66; SignalMinAtrSize = 0.3; RequireNonInsideSignalBar = true;
                MinAtrForEntry = 0.5; // Example: Min 0.5 points (adjust for instrument)
                MaxAtrForEntry = 5.0; // Example: Max 5.0 points (adjust for instrument)
            }
            else if (State == State.Configure)
            {
                // SetCommission is not supported in NinjaScript. Set commissions in the platform's strategy properties.
                // === Add secondary 1-min series ===
                AddDataSeries(BarsPeriodType.Minute, 1);
            }
            else if (State == State.DataLoaded)
            {
                if (EnableLogging)
                {
                    logManager = new LogManager(this);
                    logManager.InitializeLogs();
                    logManager.Log($"Strategy Parameters Loaded: {Name}", "Info");
                }

                ema20 = EMA(Close, EmaPeriod);
                atr14 = ATR(AtrPeriod);
                swing = Swing(SwingStrength);
                smaBarRange = SMA(Range(), AvgRangePeriod); // Corrected SMA input: Use Range() indicator result

                // === Initialize secondary series indicators ===
                if (BarsArray.Length > 1 && BarsArray[1] != null) // Check secondary series exists
                {
                    atr1Min = ATR(BarsArray[1], 14);
                    ema1Min = EMA(BarsArray[1], 20);
                }
                else if (EnableWarningLogging) // Log if secondary series is missing
                {
                    logManager?.Log("Secondary Data Series (1-min) not loaded or missing. Multi-timeframe features relying on it will be disabled.", "Warning", true);
                }

                statsTracker = new StatsTracker(this, logManager);
                tradeManager = new TradeManager(this, logManager, statsTracker, atr14, swing);

                // Clear any existing draw objects on reload/optimization
                // Use RemoveDrawObject for specific tags if needed, ClearDrawObjects() is not valid.
                RemoveDrawObject("ContextText");
                RemoveDrawObject("PullbackText");
                // Add more RemoveDrawObject calls as needed for other persistent objects.
            }
             else if (State == State.Terminated)
             {
                 // Clean up chart visuals
                 // ChartPanel.Children is not accessible in NT8. Custom WPF overlays are not supported.
                 // Use Draw.Text for chart annotations if needed.

                 if (logManager != null)
                 {
                     string summary = $"=== {Name} Summary ===\n" + // Use Name
                                     $"Run Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                                     $"Total Net Profit: {SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit:C}\n" +
                                     $"Total Trades: {SystemPerformance.AllTrades.Count}\n" +
                                     // The following properties do not exist as written in NT8. Use correct API or remove.
                                      $"Win Rate: {(SystemPerformance.AllTrades.Count > 0 ? ((double)SystemPerformance.AllTrades.WinningTrades.Count / SystemPerformance.AllTrades.Count) : 0):P2}\n" +
                                      $"Profit Factor: {SystemPerformance.AllTrades.TradesPerformance.ProfitFactor:F2}\n" +
                                      $"Avg Trade: {(SystemPerformance.AllTrades.Count > 0 ? (SystemPerformance.AllTrades.TradesPerformance.Currency.CumProfit / SystemPerformance.AllTrades.Count) : 0):C}\n" +
                                      $"Avg Win: {(SystemPerformance.AllTrades.WinningTrades.Count > 0 ? (SystemPerformance.AllTrades.WinningTrades.TradesPerformance.Currency.CumProfit / SystemPerformance.AllTrades.WinningTrades.Count) : 0):C}\n" +
                                      $"Avg Loss: {(SystemPerformance.AllTrades.LosingTrades.Count > 0 ? (SystemPerformance.AllTrades.LosingTrades.TradesPerformance.Currency.CumProfit / SystemPerformance.AllTrades.LosingTrades.Count) : 0):C}\n" +
                                     $"=================================";
                     logManager.Log(summary, "Summary", true);
                     logManager.AppendToTradeLog(summary);
                     logManager.Close();
                 }
             }
        }
        #endregion

        #region Core Logic: OnBarUpdate (With Session Reset and Debug Visuals)
                                // === MULTI-TIMEFRAME OnBarUpdate SCAFFOLDING ===
                        
                                // --- H1/H2 and L1/L2 Second Entry Detection ---
                                // Detects and marks valid H1/H2 (bull) and L1/L2 (bear) second entries.
                                // Integrates with entry and failure setup logic.
                                private void DetectSecondEntries()
                                {
                                    // H1/H2: Bullish second entry after a pullback in a bull trend
                                    if (currentMarketContext == MarketContext.BullTrend)
                                    {
                                        // H1: First pullback low, H2: Second pullback low
                                        if (barsSinceLow1 == 0 && barsSinceLow2 > 0)
                                        {
                                            Draw.Dot(this, "H1_" + CurrentBar, false, 0, Low[0] - TickSize * 2, Brushes.Blue);
                                            logManager?.Log($"H1 (first leg down) detected at bar {CurrentBar}", "Trace");
                                        }
                                        if (barsSinceLow2 == 0)
                                        {
                                            Draw.Dot(this, "H2_" + CurrentBar, false, 0, Low[0] - TickSize * 4, Brushes.DarkBlue);
                                            logManager?.Log($"H2 (second entry long) detected at bar {CurrentBar}", "Info");
                                            // Optionally, trigger entry logic here
                                        }
                                    }
                                    // L1/L2: Bearish second entry after a pullback in a bear trend
                                    if (currentMarketContext == MarketContext.BearTrend)
                                    {
                                        if (barsSinceHigh1 == 0 && barsSinceHigh2 > 0)
                                        {
                                            Draw.Dot(this, "L1_" + CurrentBar, false, 0, High[0] + TickSize * 2, Brushes.Orange);
                                            logManager?.Log($"L1 (first leg up) detected at bar {CurrentBar}", "Trace");
                                        }
                                        if (barsSinceHigh2 == 0)
                                        {
                                            Draw.Dot(this, "L2_" + CurrentBar, false, 0, High[0] + TickSize * 4, Brushes.DarkOrange);
                                            logManager?.Log($"L2 (second entry short) detected at bar {CurrentBar}", "Info");
                                            // Optionally, trigger entry logic here
                                        }
                                    }
                                }
                
                        // --- TRENDLINE/CHANNEL DETECTION AND DRAWING ---
                        // Detects and draws main trendlines and channels using recent swing highs/lows.
                        // Marks channel overshoots, trendline breaks, and micro trendlines for discretionary review.
                        private void UpdateTrendlinesAndChannels()
                        {
                            // Find the two most recent swing highs and lows
                            int swingHigh1 = -1, swingHigh2 = -1, swingLow1 = -1, swingLow2 = -1;
                            double swingHigh1Price = 0, swingHigh2Price = 0, swingLow1Price = 0, swingLow2Price = 0;
                            int lookback = 50; // How far back to search for swings
                
                            // Note: This logic finds swings but doesn't validate if they form a reliable trendline yet.
                            for (int i = 0; i < lookback && i < CurrentBar; i++) // Start from 0 bars ago
                            {
                                if (swing.SwingHighBar(i, 0, SwingStrength) == 0 && swingHigh1 == -1)
                                {
                                    swingHigh1 = i;
                                    swingHigh1Price = swing.SwingHigh[i];
                                }
                                else if (swing.SwingHighBar(i, 0, SwingStrength) == 0 && swingHigh2 == -1)
                                {
                                    swingHigh2 = i;
                                    swingHigh2Price = swing.SwingHigh[i];
                                }
                                if (swing.SwingLowBar(i, 0, SwingStrength) == 0 && swingLow1 == -1)
                                {
                                    swingLow1 = i;
                                    swingLow1Price = swing.SwingLow[i];
                                }
                                else if (swing.SwingLowBar(i, 0, SwingStrength) == 0 && swingLow2 == -1)
                                {
                                    swingLow2 = i;
                                    swingLow2Price = swing.SwingLow[i];
                                }
                                if (swingHigh1 != -1 && swingHigh2 != -1 && swingLow1 != -1 && swingLow2 != -1)
                                    break;
                            }
                
                            // Draw main trendline (bull: lows, bear: highs)
                            if (swingLow1 >= 0 && swingLow2 >= 0)
                            {
                                // Use Draw.Line - takes barsAgo, price
                                Draw.Line(this, "MainBullTrend_" + CurrentBar, false, swingLow2, swingLow2Price, swingLow1, swingLow1Price, Brushes.LimeGreen, DashStyleHelper.Solid, 2);
                                if(DebugMode) logManager?.Log($"Bull trendline drawn: [{swingLow2} bars ago, {swingLow2Price:F2}] to [{swingLow1} bars ago, {swingLow1Price:F2}]", "Trace");
                            }
                            if (swingHigh1 >= 0 && swingHigh2 >= 0)
                            {
                                Draw.Line(this, "MainBearTrend_" + CurrentBar, false, swingHigh2, swingHigh2Price, swingHigh1, swingHigh1Price, Brushes.Red, DashStyleHelper.Solid, 2);
                                if(DebugMode) logManager?.Log($"Bear trendline drawn: [{swingHigh2} bars ago, {swingHigh2Price:F2}] to [{swingHigh1} bars ago, {swingHigh1Price:F2}]", "Trace");
                            }
                
                            // Draw channel lines (parallel to main trendline, through opposite swing)
                            if (swingLow1 >= 0 && swingLow2 >= 0 && swingHigh1 >= 0 && swingLow1 != swingLow2) // Added check for different bars
                            {
                                double bullSlope = (swingLow1Price - swingLow2Price) / (swingLow2 - swingLow1); // Slope is dy/dx (price/barsAgo difference)
                                double channelHigh = swingLow1Price + bullSlope * (swingLow1 - swingHigh1); // Project price at swingHigh1
                                Draw.Line(this, "BullChannel_" + CurrentBar, false, swingHigh1, channelHigh, swingHigh1 + (swingLow1-swingLow2), channelHigh + bullSlope*(swingLow1-swingLow2), Brushes.Lime, DashStyleHelper.Dash, 1); // Draw segment
                            }
                            if (swingHigh1 >= 0 && swingHigh2 >= 0 && swingLow1 >= 0 && swingHigh1 != swingHigh2)
                            {
                                double bearSlope = (swingHigh1Price - swingHigh2Price) / (swingHigh2 - swingHigh1);
                                double channelLow = swingHigh1Price + bearSlope * (swingHigh1 - swingLow1);
                                Draw.Line(this, "BearChannel_" + CurrentBar, false, swingLow1, channelLow, swingLow1 + (swingHigh1-swingHigh2), channelLow + bearSlope*(swingHigh1-swingHigh2), Brushes.Red, DashStyleHelper.Dash, 1);
                            }
                
                            // Detect and mark channel overshoots (price exceeds channel line)
                            // Simplified: Check if current high/low breaks the drawn channel line (if it exists)
                            // More robust check would involve calculating projected channel line value at current bar
                            // Detect and mark channel overshoots (price exceeds channel line)
                            if (swingLow1 != -1 && swingLow2 != -1 && swingHigh1 != -1)
                            {
                                double bullSlope = (swingLow1Price - swingLow2Price) / (swingLow1 - swingLow2);
                                double channelHigh = swingLow1Price + bullSlope * (swingLow1 - swingHigh1);
                                if (High[0] > channelHigh)
                                {
                                    Draw.ArrowUp(this, "BullOvershoot_" + CurrentBar, false, 0, High[0] + TickSize * 2, Brushes.LimeGreen);
                                    logManager?.Log($"Bull channel overshoot at bar {CurrentBar}: High={High[0]:F2} > Channel={channelHigh:F2}", "Info");
                                }
                            }
                            if (swingHigh1 != -1 && swingHigh2 != -1 && swingLow1 != -1)
                            {
                                double bearSlope = (swingHigh1Price - swingHigh2Price) / (swingHigh1 - swingHigh2);
                                double channelLow = swingHigh1Price + bearSlope * (swingHigh1 - swingLow1);
                                if (Low[0] < channelLow)
                                {
                                    Draw.ArrowDown(this, "BearOvershoot_" + CurrentBar, false, 0, Low[0] - TickSize * 2, Brushes.Red);
                                    logManager?.Log($"Bear channel overshoot at bar {CurrentBar}: Low={Low[0]:F2} < Channel={channelLow:F2}", "Info");
                                }
                            }
                            {
                                Draw.ArrowDown(this, "BearOvershoot_" + CurrentBar, false, 0, Low[0] - TickSize * 2, Brushes.Red);
                                logManager?.Log($"Potential Bear channel overshoot at bar {CurrentBar}", "Info");
                            }
                
                            // [Optional] Micro trendline drawing - Removed for simplification, focus on main lines
                            // ... (Removed micro trendline detection and drawing loop) ...

                            // Clean up old lines
                            RemoveDrawObject("MainBullTrend_" + (CurrentBar - lookback -1)); RemoveDrawObject("MainBearTrend_" + (CurrentBar - lookback - 1));
                            RemoveDrawObject("BullChannel_" + (CurrentBar - lookback -1)); RemoveDrawObject("BearChannel_" + (CurrentBar - lookback - 1));
                        }
                // This method is the heart of the strategy, processing each bar to:
                // - Track market context (trend/range)
                // - Count pullbacks and H/L legs
                // - Analyze each bar (winner, tails, close)
                // - Detect explicit failure setups (failed breakouts, reversals, second entries)
                // - Manage session/time awareness (avoid trades near session end)
                // - Log context changes, trade signals, and failures
                // - Provide scaffolding for trendline/channel logic and discretionary review
        protected override void OnBarUpdate()
        {
            try // Basic error handling
            {
                // Ensure enough bars for each series
                if (CurrentBars == null || CurrentBars.Length == 0 || CurrentBars[BarsInProgress] < BarsRequiredToTrade)
                    return;

                // Session/time awareness: avoid trades near session end
                DateTime barTime = Time[0];
                // --- SESSION/TIME AWARENESS ---
                // Avoid new trades near session end; exit open positions if session is over
                if (barTime.Hour > SessionEndHour || (barTime.Hour == SessionEndHour && barTime.Minute >= SessionEndMinute))
                {
                    if (Position.MarketPosition != MarketPosition.Flat)
                        tradeManager.ExitPosition("Session End", "SessionEnd");
                    logManager?.Log($"Session end reached at {barTime:HH:mm}. No new trades allowed.", "Info");
                    return;
                }

                // Separate logic for each series
                if (BarsInProgress == 0)
                {
                    // --- PRIMARY SERIES (5-min) LOGIC ---

                    // --- Daily/Manual State Reset ---
                    firstBarOfSession = Bars.IsFirstBarOfSession;
                    bool needsReset = firstBarOfSession || ManualReset;

                    if (needsReset)
                    {
                        ResetDailyState();
                        if (ManualReset) ManualReset = false;
                        logManager?.Log($"{(firstBarOfSession ? "Session" : "Manual")} State Reset performed at {Time[0]}", "Info");
                    }

                    // --- Pre-computation & Context ---
                    double currentAtr = atr14[0];
                    if (currentAtr.ApproxCompare(0) <= 0 || double.IsNaN(currentAtr)) return;

                    statsTracker.UpdateDailyPnL();
                    if (statsTracker.DailyLossExceeded(MaxDailyLoss)) { if(Position.MarketPosition != MarketPosition.Flat) tradeManager.ExitPosition("Daily Loss Hit", "DL_Exit"); return; }

                    // --- Bar-by-bar analysis: who won, tails, close position ---
                    string barAnalysis = AnalyzeBar(0);
                    logManager?.Log($"Bar Analysis: {barAnalysis}", "Debug");

                    // --- SWING/CHANNEL/STRUCTURE LOGIC SCAFFOLDING ---
                    // This section can be expanded to include trendline/channel drawing and logic.
                    // For now, we update swing highs/lows for use in structure stops/targets and context.
                    // [TODO: Add trendline/channel detection and visualization for discretionary review.]
                    // Example: Draw trendlines between recent swing points, detect channel overshoots, etc.
                    bool newSwingHigh = false, newSwingLow = false;
                    if (swing.SwingHigh[0] > 0) { if(swing.SwingHighBar(0, 0, SwingStrength) == 0 && swing.SwingHighBar(0, 0, SwingStrength) != swing.SwingHighBar(1, 0, SwingStrength)) newSwingHigh = true; lastSwingHigh = swing.SwingHigh[0]; lastSwingHighBar = swing.SwingHighBar(0, 0, SwingStrength); } else { lastSwingHighBar = -1; }
                    if (swing.SwingLow[0] > 0) { if(swing.SwingLowBar(0, 0, SwingStrength) == 0 && swing.SwingLowBar(0, 0, SwingStrength) != swing.SwingLowBar(1, 0, SwingStrength)) newSwingLow = true; lastSwingLow = swing.SwingLow[0]; lastSwingLowBar = swing.SwingLowBar(0, 0, SwingStrength); } else { lastSwingLowBar = -1; }

                    // Determine Context & Filters
                    MarketContext previousContext = currentMarketContext;
                    currentMarketContext = DetermineMarketContext(EmaTrendLookback);
                    if (previousContext != currentMarketContext)
                        logManager?.Log($"Context Change: {previousContext} -> {currentMarketContext} at bar {CurrentBar}", "Info");

                    isBarbWire = CheckForBarbWire(currentAtr);

                    // Update High/Low Counting State
                    UpdateHighLowCounters(previousContext, newSwingHigh, newSwingLow);

                    // Update Debug Visuals
                    UpdateDebugVisuals();
                    // --- TRENDLINE/CHANNEL LOGIC: Draw and log for discretionary/manual review ---
                    // UpdateTrendlinesAndChannels(); // Keep this commented out unless specifically needed/debugged
                    // --- ADVANCED PATTERN DETECTION ---
                    // DetectWedgePattern(); // Removed call
                    // DetectDoubleTopBottom(); // Removed call
                    // DetectMeasuredMove(); // Removed call
                    // These methods are not defined. Implement or remove for NT8 compliance.

                    if (isBarbWire && DebugMode) logManager.Log($"Barb Wire detected - No Trading. Bar: {CurrentBar}", "Debug");

                    // --- Position Management ---
                    if (Position.MarketPosition != MarketPosition.Flat)
                    {
                        bool useStructureTrail = UseStructureStop && UseStructureTrailStop && (Position.MarketPosition == MarketPosition.Long ? lastSwingLowBar >= 0 : lastSwingHighBar >= 0);
                        tradeManager.ManageStopLoss(useStructureTrail, StopLossAtrMultiplier, StructureStopAtrBuffer);
                    }

                    // --- Entry Logic (With Context Confluence Filter) ---
                    // --- Volatility Filter ---
                    double currentAtrValue = atr14[0]; // ATR in points
                    // Check if parameters are set (avoid filtering if Min=0 and Max=0)
                    bool checkVolatility = !(MinAtrForEntry == 0 && MaxAtrForEntry == 0);
                    bool volatilityOk = !checkVolatility || (currentAtrValue >= MinAtrForEntry && currentAtrValue <= MaxAtrForEntry);

                    if (checkVolatility && !volatilityOk && DebugMode)
                        logManager?.Log($"Volatility Filter Block: ATR={currentAtrValue:F2} not within [{MinAtrForEntry:F2} - {MaxAtrForEntry:F2}]. Bar={CurrentBar}", "Debug");

                    if (Position.MarketPosition == MarketPosition.Flat && !isBarbWire && volatilityOk)
                    {
                        // Trend Entries
                        if (currentMarketContext == MarketContext.BullTrend)
                        { CheckEMAEntry(); CheckHighLowEntry(); CheckBreakoutPullbackEntry(MarketPosition.Long); }
                        else if (currentMarketContext == MarketContext.BearTrend)
                        { CheckEMAEntry(); CheckHighLowEntry(); CheckBreakoutPullbackEntry(MarketPosition.Short); }
                        // Range Entries
                        else if (currentMarketContext == MarketContext.TradingRange)
                        { CheckRangeFadeEntry(); }

                        // Other Patterns (Check context internally if needed)
                        CheckOutsideBarEntry();
                        CheckFailedFinalFlagEntry();

                        // --- Failure Setups: failed breakouts, failed reversals, failed second entries ---
                        // DetectFailureSetups(); // Keep this commented out for now

                        // --- ACTIONABLE H2/L2 SECOND ENTRY LOGIC ---
                        // Take a long trade on H2 in bull trend, short trade on L2 in bear trend, if signal bar is good
                        if (currentMarketContext == MarketContext.BullTrend && barsSinceLow2 == 0 && IsGoodSignalBar(0, MarketPosition.Long))
                        {
                            double entryPrice = High[0] + TickSize; // Stop entry above signal bar
                            double stopPrice = Low[0] - atr14[0] * StopLossAtrMultiplier;
                            double target1 = entryPrice + atr14[0] * FallbackTarget1AtrMultiplier;
                            double target2 = entryPrice + atr14[0] * FallbackTarget2AtrMultiplier;
                            int qty = tradeManager.CalculatePositionSize(entryPrice - stopPrice);
                            tradeManager.EnterTrade(MarketPosition.Long, "H2_Long", entryPrice, stopPrice, target1, target2, qty);
                            Draw.ArrowUp(this, "H2_Entry_" + CurrentBar, false, 0, Low[0] - TickSize * 6, Brushes.Blue);
                            logManager?.Log($"H2 Long Entry placed at bar {CurrentBar}: Entry={entryPrice:F2}, Stop={stopPrice:F2}, Qty={qty}", "Info");
                        }
                        if (currentMarketContext == MarketContext.BearTrend && barsSinceHigh2 == 0 && IsGoodSignalBar(0, MarketPosition.Short))
                        {
                            double entryPrice = Low[0] - TickSize; // Stop entry below signal bar
                            double stopPrice = High[0] + atr14[0] * StopLossAtrMultiplier;
                            double target1 = entryPrice - atr14[0] * FallbackTarget1AtrMultiplier;
                            double target2 = entryPrice - atr14[0] * FallbackTarget2AtrMultiplier;
                            int qty = tradeManager.CalculatePositionSize(stopPrice - entryPrice);
                            tradeManager.EnterTrade(MarketPosition.Short, "L2_Short", entryPrice, stopPrice, target1, target2, qty);
                            Draw.ArrowDown(this, "L2_Entry_" + CurrentBar, false, 0, High[0] + TickSize * 6, Brushes.Orange);
                            logManager?.Log($"L2 Short Entry placed at bar {CurrentBar}: Entry={entryPrice:F2}, Stop={stopPrice:F2}, Qty={qty}", "Info");
                        }
                    }
                }
                else if (BarsInProgress == 1)
                {
                    // --- SECONDARY SERIES (1-min) LOGIC ---
                    // Reserved for future expansion (e.g., micro trendlines, session structure, etc.)
                }
            }
            catch (Exception e)
            {
                string errorMsg = $"CRITICAL ERROR in OnBarUpdate: {e.Message} --- {e.StackTrace}";
                logManager?.Log(errorMsg, "Critical", true);
                Print(errorMsg); // Print to output window as well
                if(State == State.Realtime)
                {
                    SetState(State.Terminated); // Halt on critical error in real-time
                    Alert("StrategyError", Priority.High, "Critical error, strategy terminated.", NinjaTrader.Core.Globals.InstallDir + @"\sounds\Alert1.wav", 10, Brushes.Red, Brushes.White);
                }
            }
        }

        // --- Bar-by-bar winner/tail/close analysis for diagnostics and context ---
        private string AnalyzeBar(int barsAgo)
        {
            double open = Open[barsAgo];
            double close = Close[barsAgo];
            double high = High[barsAgo];
            double low = Low[barsAgo];
            double body = close - open;
            double upperTail = high - Math.Max(open, close);
            double lowerTail = Math.Min(open, close) - low;
            string winner = body > 0 ? "Bull" : body < 0 ? "Bear" : "Doji";
            string closePos = close > open ? "Closed High" : close < open ? "Closed Low" : "Closed Mid";
            return $"Bar {CurrentBar - barsAgo}: {winner}, {closePos}, Body={body:F2}, UpperTail={upperTail:F2}, LowerTail={lowerTail:F2}";
        }

        // --- Detect failed breakouts, failed reversals, failed second entries as explicit setups ---
        /// <summary>
        /// Detects and logs explicit failure setups:
        /// - Failed breakouts (breakout bar immediately reversed)
        /// - Failed reversals (reversal bar fails and price resumes prior trend)
        /// - Failed second entries (H2/L2 fails and reverses)
        /// This is a scaffold for further expansion; add more nuanced logic as needed.
        /// </summary>
        private void DetectFailureSetups()
        {
            // --- Failed Breakout Detection ---
            // If previous bar closed above prior swing high (bull breakout), but current bar closes back below swing high, flag as failed breakout.
            if (lastSwingHighBar == 1 && Close[1] > lastSwingHigh && Close[0] < lastSwingHigh)
            {
                logManager?.Log($"Failed Bull Breakout detected at bar {CurrentBar}: Breakout above {lastSwingHigh:F2} reversed.", "Warning");
                // [TODO: Add logic to mark chart, trigger counter-trend setup, etc.]
            }
            if (lastSwingLowBar == 1 && Close[1] < lastSwingLow && Close[0] > lastSwingLow)
            {
                logManager?.Log($"Failed Bear Breakout detected at bar {CurrentBar}: Breakout below {lastSwingLow:F2} reversed.", "Warning");
                // [TODO: Add logic to mark chart, trigger counter-trend setup, etc.]
            }

            // --- Failed Reversal Detection ---
            // If a reversal bar (e.g., bullish after bear trend) is immediately negated, flag as failed reversal.
            // [TODO: Refine with more context, e.g., bar patterns, context, and EMA position.]
            if (currentMarketContext == MarketContext.BullTrend && Close[1] < Open[1] && Close[0] < Close[1])
            {
                logManager?.Log($"Failed Bull Reversal: Bearish follow-through after attempted bull reversal at bar {CurrentBar}.", "Warning");
            }
            if (currentMarketContext == MarketContext.BearTrend && Close[1] > Open[1] && Close[0] > Close[1])
            {
                logManager?.Log($"Failed Bear Reversal: Bullish follow-through after attempted bear reversal at bar {CurrentBar}.", "Warning");
            }

            // --- Failed Second Entry Detection (H2/L2) ---
            // If a second entry (H2/L2) fails and price reverses, flag as failed second entry.
            // [TODO: Refine with actual H2/L2 logic and state tracking.]
            if (barsSinceHigh2 == 0 && Close[0] < Open[0])
            {
                logManager?.Log($"Failed H2 (second entry long) at bar {CurrentBar}: Entry failed, bar closed bearish.", "Warning");
            }
            if (barsSinceLow2 == 0 && Close[0] > Open[0])
            {
                logManager?.Log($"Failed L2 (second entry short) at bar {CurrentBar}: Entry failed, bar closed bullish.", "Warning");
            }

            // [TODO: Expand with more nuanced failure logic, e.g., failed flags, failed wedges, etc.]
        }
        #endregion

        #region State Reset Method
        private void ResetDailyState()
        {
            statsTracker?.ResetDailyStats();
            tradeManager?.ResetDaily(); // Call reset on trade manager

            // Reset strategy state variables
            currentMarketContext = MarketContext.Undetermined;
            lastSwingHigh = double.MinValue; lastSwingLow = double.MaxValue;
            lastSwingHighBar = -1; lastSwingLowBar = -1;
            barsSinceHigh1 = 999; barsSinceHigh2 = 999; barsSinceLow1 = 999; barsSinceLow2 = 999;
            inPullback = false; barsSincePullbackStart = 0;
            isBarbWire = false;

            // Clear debug visuals from previous day
            RemoveDrawObject("ContextText");
            RemoveDrawObject("PullbackText");
            // Potentially clear other persistent Draw objects if needed
        }
        #endregion

        #region Debugging Visuals and Live Summary Dashboard
        // Enhanced: Adds a live summary dashboard overlay for context, signals, and trade stats.
        private void UpdateDebugVisuals()
        {
            if (!DebugMode || ChartControl == null) return;
            // IsChartVisible is not a NinjaScript property.

            // Build context summary
            string contextText = $"Ctx: {currentMarketContext}";
            string pullbackText = $"PB: {inPullback} ({barsSincePullbackStart}) | H1:{barsSinceHigh1} H2:{barsSinceHigh2} | L1:{barsSinceLow1} L2:{barsSinceLow2}";

            // Add trade stats (PnL, win rate, drawdown, open position)
            string tradeStats = "";
            if (statsTracker != null)
            {
                tradeStats = $"PnL: {statsTracker.DailyPnL:F2} | MaxDD: {statsTracker.DailyMaxDrawdown:F2}";
            }
            string positionText = Position.MarketPosition != MarketPosition.Flat
                ? $"Pos: {Position.MarketPosition} @ {Position.AveragePrice:F2} | Qty: {Position.Quantity}"
                : "Pos: Flat";

            // Compose dashboard text
            string dashboardText = $"{contextText} | {pullbackText}\n{tradeStats} | {positionText} | Bar: {CurrentBar}";

            // Use Dispatcher for UI updates
            // ChartPanel.Children and custom WPF overlays are not supported in NT8.
            // Use Draw.Text for dashboard overlay.
            Draw.Text(this, "DashboardText", dashboardText, 0, High[0] + 2 * TickSize, Brushes.DimGray);

            // Draw Dots - Use CurrentBar-barsAgo calculation
            if (lastSwingHighBar >= 0 && lastSwingHighBar < Bars.Count)
                Draw.Dot(this, $"SwingH_{CurrentBar}", false, lastSwingHighBar, lastSwingHigh + TickSize * 2, Brushes.Red);
            if (lastSwingLowBar >= 0 && lastSwingLowBar < Bars.Count)
                Draw.Dot(this, $"SwingL_{CurrentBar}", false, lastSwingLowBar, lastSwingLow - TickSize * 2, Brushes.Lime);

            // Clean up old draw objects - Example: remove dots older than 20 bars
            RemoveDrawObject($"SwingH_{CurrentBar - 20}");
            RemoveDrawObject($"SwingL_{CurrentBar - 20}");
        }
        #endregion


        #region Context and Setup Identification (Refined)

        // *** ENHANCED Context Determination ***
        private MarketContext DetermineMarketContext(int lookback)
        {
            if (CurrentBar < lookback + 5 || swing == null || !ema20.IsValidDataPoint(lookback)) return MarketContext.Undetermined;

            double emaSlope = ema20[0] - ema20[lookback];
            int barsAboveEma = 0; int barsBelowEma = 0;
            for (int i = 0; i < lookback; i++)
            {
                if (!ema20.IsValidDataPoint(i)) continue; // Check indicator validity
                if (Close[i] > ema20[i]) barsAboveEma++;
                if (Close[i] < ema20[i]) barsBelowEma++;
            }

            // Need at least two swings to determine swing trend
            bool recentHigherHighs = swing.SwingHighBar(0, 0, SwingStrength) >= 0 && swing.SwingHighBar(1, 0, SwingStrength) >= 0 && swing.SwingHigh[0] > swing.SwingHigh[1];
            bool recentHigherLows = swing.SwingLowBar(0, 0, SwingStrength) >= 0 && swing.SwingLowBar(1, 0, SwingStrength) >= 0 && swing.SwingLow[0] > swing.SwingLow[1];
            bool recentLowerHighs = swing.SwingHighBar(0, 0, SwingStrength) >= 0 && swing.SwingHighBar(1, 0, SwingStrength) >= 0 && swing.SwingHigh[0] < swing.SwingHigh[1];
            bool recentLowerLows = swing.SwingLowBar(0, 0, SwingStrength) >= 0 && swing.SwingLowBar(1, 0, SwingStrength) >= 0 && swing.SwingLow[0] < swing.SwingLow[1];

            double slopeThreshold = TickSize * lookback * 0.05;
            double pricePositionThreshold = 0.7;

            if (emaSlope > slopeThreshold && barsAboveEma > lookback * pricePositionThreshold && recentHigherLows && !recentLowerHighs)
                return MarketContext.BullTrend;
            else if (emaSlope < -slopeThreshold && barsBelowEma > lookback * pricePositionThreshold && recentLowerLows && !recentHigherLows)
                return MarketContext.BearTrend;
            else
                return MarketContext.TradingRange;
        }

        // CheckForBarbWire remains the same
        private bool CheckForBarbWire(double currentAtr)
        {
            if (CurrentBar < BarsRequiredToTrade + BarbWireLookback || currentAtr <= TickSize || !ema20.IsValidDataPoint(BarbWireLookback)) return false; // Added EMA validity check

            int overlapCount = 0; int dojiCount = 0; bool nearEma = false;
            for (int i = 0; i < BarbWireLookback; i++)
            {
                double range = High[i] - Low[i]; if (range <= TickSize) continue;
                double body = Math.Abs(Close[i] - Open[i]);
                bool isDojiLike = body <= range * (BarbWireMaxBodyPercent / 100.0);
                bool isSmallRange = range <= currentAtr * BarbWireMaxRangeAtr;
                bool isNearEma = Math.Abs(Close[i] - ema20[i]) <= currentAtr * BarbWireEmaProximityAtr;
                if (isDojiLike && isSmallRange) dojiCount++;
                if (isNearEma) nearEma = true;
                if (i > 0) { double highOverlap = Math.Min(High[i], High[i - 1]); double lowOverlap = Math.Max(Low[i], Low[i - 1]); if (highOverlap > lowOverlap) overlapCount++; }
            }
            return nearEma && overlapCount >= BarbWireLookback - 2 && dojiCount >= 2;
        }

        // *** ENHANCED H/L Counting Logic ***
        private void UpdateHighLowCounters(MarketContext previousContext, bool newSwingHigh, bool newSwingLow)
        {
             // Increment counters for time passing
            if (barsSinceHigh1 <= 998) barsSinceHigh1++; if (barsSinceHigh2 <= 998) barsSinceHigh2++;
            if (barsSinceLow1 <= 998) barsSinceLow1++; if (barsSinceLow2 <= 998) barsSinceLow2++;
            if (inPullback) barsSincePullbackStart++;

            // --- Pullback State Management ---
            bool justStartedPullback = false;
            if (!inPullback)
            {
                bool crossAgainstTrend = (previousContext == MarketContext.BullTrend && Low[0] <= ema20[0])
                                        || (previousContext == MarketContext.BearTrend && High[0] >= ema20[0]);
                bool contextToRange = (previousContext != MarketContext.TradingRange && currentMarketContext == MarketContext.TradingRange);

                 if(crossAgainstTrend || contextToRange)
                 {
                     inPullback = true; barsSincePullbackStart = 0; justStartedPullback = true;
                     if (DebugMode) logManager?.Log($"Pullback Started: Bar={CurrentBar}, Cross={crossAgainstTrend}, CtxChg={contextToRange}", "Trace");
                 }
            }
            else // Currently in pullback
            {
                bool trendResumed = (previousContext == MarketContext.BullTrend && currentMarketContext == MarketContext.BullTrend && newSwingHigh)
                                  || (previousContext == MarketContext.BearTrend && currentMarketContext == MarketContext.BearTrend && newSwingLow);
                 if(trendResumed)
                 {
                     inPullback = false; barsSincePullbackStart = 0;
                     if (DebugMode) logManager?.Log($"Pullback Ended (Trend Resumed): Bar={CurrentBar}", "Trace");
                 }
            }

            // --- H/L Count Reset ---
            bool resetH1 = (justStartedPullback && currentMarketContext == MarketContext.BearTrend && barsSincePullbackStart >= MinBarsForPullbackLeg - 1)
                         || (!inPullback && currentMarketContext != MarketContext.BearTrend); // Normal count in Bull/Range
            bool resetL1 = (justStartedPullback && currentMarketContext == MarketContext.BullTrend && barsSincePullbackStart >= MinBarsForPullbackLeg - 1)
                         || (!inPullback && currentMarketContext != MarketContext.BullTrend); // Normal count in Bear/Range


            if (High[0] > High[1]) { if(resetH1) { barsSinceHigh2 = barsSinceHigh1; barsSinceHigh1 = 0; if (DebugMode) logManager?.Log($"H1 Reset (Pullback/Trend): Bar={CurrentBar}", "Trace"); }}
            if (Low[0] < Low[1]) { if(resetL1) { barsSinceLow2 = barsSinceLow1; barsSinceLow1 = 0; if (DebugMode) logManager?.Log($"L1 Reset (Pullback/Trend): Bar={CurrentBar}", "Trace"); }}
        }


        // *** ENHANCED Signal Bar Check ***
        private bool IsGoodSignalBar(int barsAgo, MarketPosition direction)
        {
            // Check if barsAgo is valid
            if (barsAgo < 0 || barsAgo >= CurrentBar || !smaBarRange.IsValidDataPoint(barsAgo) || !atr14.IsValidDataPoint(barsAgo)) return false;

            double high = High[barsAgo];
            double low = Low[barsAgo];
            double open = Open[barsAgo];
            double close = Close[barsAgo];
            double range = high - low;
            double currentAtr = atr14[barsAgo];

            // Filter 1: Basic Range Checks
            if (range.ApproxCompare(TickSize) <= 0) { if(DebugMode) logManager?.Log($"BadSignal: Zero Range Bar {CurrentBar-barsAgo}", "Trace"); return false; }
            if (range < currentAtr * SignalMinAtrSize) { if(DebugMode) logManager?.Log($"BadSignal: Too Small vs ATR Bar {CurrentBar-barsAgo}", "Trace"); return false; }

             // Filter 2: Inside Bar Check (Optional)
            if (RequireNonInsideSignalBar && barsAgo > 0 && IsInsideBar(barsAgo)) { if(DebugMode) logManager?.Log($"BadSignal: Inside Bar {CurrentBar-barsAgo}", "Trace"); return false; }

            // Filter 3: Body, Tail, Close Position Checks
            double bodySize = Math.Abs(close - open);
            double bodyPercent = (range > 0) ? (bodySize / range) * 100.0 : 0; // Check range > 0
            double upperTail = high - Math.Max(close, open);
            double lowerTail = Math.Min(close, open) - low;
            double upperTailPercent = (range > 0) ? (upperTail / range) * 100.0 : 0;
            double lowerTailPercent = (range > 0) ? (lowerTail / range) * 100.0 : 0;
            double closePositionPercent = (range > 0) ? ((close - low) / range) * 100.0 : 50; // Default mid if no range

            bool isBullishBar = close > open;
            bool isBearishBar = close < open;
            bool isStrongTrendBody = bodyPercent >= SignalMinBodyPercent;

            if (direction == MarketPosition.Long)
            {
                if (isBearishBar && isStrongTrendBody) { if(DebugMode) logManager?.Log($"BadSignal(L): Strong Bear Body Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (upperTailPercent > SignalMaxOpposingTailPercent) { if(DebugMode) logManager?.Log($"BadSignal(L): Large Upper Tail Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (closePositionPercent < (100 - SignalFavorableClosePercent)) { if(DebugMode) logManager?.Log($"BadSignal(L): Close Too Low Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (!isBullishBar && !isStrongTrendBody && closePositionPercent < 75) { if(DebugMode) logManager?.Log($"BadSignal(L): Weak Close (Non-Bull) Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                return true; // Passed all checks
            }
            else // Short Direction
            {
                if (isBullishBar && isStrongTrendBody) { if(DebugMode) logManager?.Log($"BadSignal(S): Strong Bull Body Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (lowerTailPercent > SignalMaxOpposingTailPercent) { if(DebugMode) logManager?.Log($"BadSignal(S): Large Lower Tail Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (closePositionPercent > SignalFavorableClosePercent) { if(DebugMode) logManager?.Log($"BadSignal(S): Close Too High Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                if (!isBearishBar && !isStrongTrendBody && closePositionPercent > 25) { if(DebugMode) logManager?.Log($"BadSignal(S): Weak Close (Non-Bear) Bar {CurrentBar-barsAgo}", "Trace"); return false; }
                return true; // Passed all checks
            }
        }

        // --- Other Setup Checks (CheckEMAEntry, CheckHighLowEntry, CheckRangeFadeEntry, CheckOutsideBarEntry, CheckFailedFinalFlagEntry, CheckBreakoutPullbackEntry, IsInsideBar) ---
        // Implementations remain the same as V2/previous refined version, calling the enhanced IsGoodSignalBar
        private void CheckEMAEntry() { /* ... V2 Code ... */ }
        // --- Enhanced H1/H2 and L1/L2 Entry with Contextual Confluence ---
        private void CheckHighLowEntry()
        {
            // H2 Long Entry in Bull Trend with confluence checks
            // H2 Long Entry in Bull Trend
            if (currentMarketContext == MarketContext.BullTrend && barsSinceHigh2 < 990 && barsSinceHigh1 < barsSinceHigh2 && barsSinceHigh1 >= 0)
            {
                // Confluence Checks
                bool pullbackWasWeak = true;
                int lookback = Math.Min(barsSinceHigh2 + 1, CurrentBar); // Bars since H2 signal bar formed
                int bearBarCount = 0;
                for (int i = 0; i < lookback; i++) { if (Close[i] < Open[i]) bearBarCount++; }
                // If more than half the bars in the pullback were bearish, consider it a strong pullback
                if (bearBarCount > lookback / 2) pullbackWasWeak = false;

                bool nearEma = barsSinceHigh2 < CurrentBar && Low[barsSinceHigh2] > ema20[barsSinceHigh2] - atr14[barsSinceHigh2] * 0.5; // Add check for valid index
                if (pullbackWasWeak && nearEma && IsGoodSignalBar(barsSinceHigh2, MarketPosition.Long))
                {
                    logManager?.Log($"H2 Long Setup Detected (Passed Confluence): Bar={CurrentBar}, PBWeak={pullbackWasWeak}, NearEMA={nearEma}", "Debug");
                    // Call EnterTrade using the signal bar index to get H/L
                    // Calculate stop, targets, and quantity for H2 long entry
                    double entryPriceH2 = High[barsSinceHigh2] + TickSize;
                    double stopPriceH2 = Low[barsSinceHigh2] - atr14[barsSinceHigh2] * StopLossAtrMultiplier;
                    double target1H2 = entryPriceH2 + atr14[barsSinceHigh2] * FallbackTarget1AtrMultiplier;
                    double target2H2 = entryPriceH2 + atr14[barsSinceHigh2] * FallbackTarget2AtrMultiplier;
                    int qtyH2 = tradeManager.CalculatePositionSize(entryPriceH2 - stopPriceH2);
                    tradeManager.EnterTrade(MarketPosition.Long, "H2_Entry", entryPriceH2, stopPriceH2, target1H2, target2H2, qtyH2);
                }
                else if (DebugMode)
                {
                    logManager?.Log($"H2 Long Setup Filtered: Bar={CurrentBar}, PBWeak={pullbackWasWeak}, NearEMA={nearEma}, GoodSignal={IsGoodSignalBar(barsSinceHigh2, MarketPosition.Long)}", "Debug");
                }
            }
            // L2 Short Entry in Bear Trend
            else if (currentMarketContext == MarketContext.BearTrend && barsSinceLow2 < 990 && barsSinceLow1 < barsSinceLow2 && barsSinceLow1 >= 0)
            {
                // Confluence Checks
                bool rallyWasWeak = true;
                int lookback = Math.Min(barsSinceLow2 + 1, CurrentBar); // Bars since L2 signal bar formed
                int bullBarCount = 0;
                for (int i = 0; i < lookback; i++) { if (Close[i] > Open[i]) bullBarCount++; }
                // If more than half the bars in the rally were bullish, consider it a strong rally
                if (bullBarCount > lookback / 2) rallyWasWeak = false;
                bool nearEma = barsSinceLow2 < CurrentBar && High[barsSinceLow2] < ema20[barsSinceLow2] + atr14[barsSinceLow2] * 0.5; // Add check for valid index

                if (rallyWasWeak && nearEma && IsGoodSignalBar(barsSinceLow2, MarketPosition.Short))
                {
                    logManager?.Log($"L2 Short Setup Detected (Passed Confluence): Bar={CurrentBar}, RallyWeak={rallyWasWeak}, NearEMA={nearEma}", "Debug");
                    // Pass signal bar index to EnterTrade
                    // Calculate stop, targets, and quantity for L2 short entry
                    double entryPriceL2 = Low[barsSinceLow2] - TickSize;
                    double stopPriceL2 = High[barsSinceLow2] + atr14[barsSinceLow2] * StopLossAtrMultiplier;
                    double target1L2 = entryPriceL2 - atr14[barsSinceLow2] * FallbackTarget1AtrMultiplier;
                    double target2L2 = entryPriceL2 - atr14[barsSinceLow2] * FallbackTarget2AtrMultiplier;
                    int qtyL2 = tradeManager.CalculatePositionSize(stopPriceL2 - entryPriceL2);
                    tradeManager.EnterTrade(MarketPosition.Short, "L2_Entry", entryPriceL2, stopPriceL2, target1L2, target2L2, qtyL2);
                }
                else if (DebugMode)
                {
                    logManager?.Log($"L2 Short Setup Filtered: Bar={CurrentBar}, RallyWeak={rallyWasWeak}, NearEMA={nearEma}, GoodSignal={IsGoodSignalBar(barsSinceLow2, MarketPosition.Short)}", "Debug");
                }
            }
        }
        private void CheckRangeFadeEntry() { /* ... V2 Code ... */ }
        private void CheckOutsideBarEntry() { /* ... V2 Code ... */ }
        private void CheckFailedFinalFlagEntry() { /* ... V2 Code ... */ }
        private void CheckBreakoutPullbackEntry(MarketPosition trendDirection) { /* ... V2 Code ... */ }
        private bool IsInsideBar(int barsAgo, int lookbackBarsAgo = -1) { /* ... V2 Code ... */ return false; }

        #endregion

        #region Helper Enums and Classes (Updated TradeManager with ResetDaily)
        // --- TRENDLINE/CHANNEL LOGIC SCAFFOLDING ---
        // [TODO: Implement trendline/channel detection and visualization for discretionary/manual review.]
        // Example: Use recent swing highs/lows to draw trendlines and channels.
        // - Detect channel overshoots, trendline breaks, and micro trendlines.
        // - Mark these on the chart for discretionary review.
        // - Add comments and structure for further expansion.
        // See Brooks Ch 2 for detailed trendline/channel logic.

        private enum MarketContext { Undetermined, BullTrend, BearTrend, TradingRange }

        // --- LogManager Class ---
        internal class LogManager { /* ... Full implementation from previous version ... */ public LogManager(AlBrooksPriceAction strategy) { this.strategy = strategy; /*...*/ } private readonly AlBrooksPriceAction strategy; public void Log(string m, string l, bool f=false){} public void InitializeLogs(){} public void Close(){} public void AppendToTradeLog(string m){} public void LogTradeEntry(DateTime t, string d, double ep, double sp, double t1p, double t2p, int q, double a, string sn){} public void LogTradeExit(DateTime t, double xp, double p, int q, string xr, string sn, double rp){} }
        // --- StatsTracker Class ---
        internal class StatsTracker { /* ... Full implementation from previous version ... */ public StatsTracker(AlBrooksPriceAction strategy, LogManager logger) { this.strategy = strategy; this.logManager = logger; } private readonly AlBrooksPriceAction strategy; private readonly LogManager logManager; public double DailyPnL { get; private set; } public double DailyMaxDrawdown { get; private set; } private double dailyHighestEquity; public void ResetDailyStats(){ DailyPnL = 0; DailyMaxDrawdown = 0; dailyHighestEquity = 0; } public void UpdateDailyPnL(){/*... Calculate PnL ...*/ dailyHighestEquity = Math.Max(dailyHighestEquity, DailyPnL); DailyMaxDrawdown = Math.Max(DailyMaxDrawdown, dailyHighestEquity - DailyPnL);} public bool DailyLossExceeded(double ml){ return DailyPnL <= -Math.Abs(ml);} }
        // --- TradeManager Class ---
        internal class TradeManager
        {
            private readonly AlBrooksPriceAction strategy;
            private readonly LogManager logManager;
            private readonly StatsTracker statsTracker;
            private readonly ATR atrIndicator;
            private readonly Swing swingIndicator;

            private string entrySignalName;
            private Order entryOrder;
            private Order stopOrder;
            private Order target1Order;
            private Order target2Order;
            private double initialStopPrice;
            private double entryPrice;
            private double structureTarget1;
            private double measuredMoveTarget2;
            private double breakoutPrice;

            // Trade Counter (Example of daily state)
            private int dailyTradeCounter = 0;

            public TradeManager(AlBrooksPriceAction strategy, LogManager logger, StatsTracker stats, ATR atr, Swing swing)
            {
                this.strategy = strategy;
                this.logManager = logger;
                this.statsTracker = stats;
                this.atrIndicator = atr;
                this.swingIndicator = swing;
            }

            // Robust position sizing (already implemented)
            public int CalculatePositionSize(double stopDistancePoints)
            {
                // ... existing implementation ...
                if (stopDistancePoints <= 0 || strategy.Instrument == null || strategy.Instrument.MasterInstrument == null || strategy.Instrument.MasterInstrument.PointValue <= 0)
                {
                    logManager?.Log($"CalculatePositionSize: Invalid input. StopDistance={stopDistancePoints}, PointValue={strategy.Instrument?.MasterInstrument?.PointValue}", "Warning");
                    return 1; // Default to 1 contract on error
                }

                double riskPerContract = stopDistancePoints * strategy.Instrument.MasterInstrument.PointValue;
                if (riskPerContract <= 0)
                {
                    logManager?.Log($"CalculatePositionSize: Calculated risk per contract is zero or negative ({riskPerContract}). Defaulting to 1 contract.", "Warning");
                    return 1;
                }

                double idealSize = strategy.MaxRiskPerTrade / riskPerContract;
                int calculatedSize = (int)Math.Floor(idealSize);
                int finalSize = Math.Max(1, Math.Min(10, calculatedSize));
                logManager?.Log($"CalculatePositionSize: StopDist={stopDistancePoints:F2} pts, Risk/Contract=${riskPerContract:F2}, IdealSize={idealSize:F2}, CalcSize={calculatedSize}, FinalSize={finalSize} (MaxRisk=${strategy.MaxRiskPerTrade}, MaxContracts=10)", "Debug");
                return finalSize;
            }

            // === Robust Order Management ===

            public void EnterTrade(MarketPosition direction, string signalName, double entryPrice, double stopPrice, double target1, double target2, int quantity)
            {
                // Ensure unique signal name
                dailyTradeCounter++;
                string uniqueSignal = $"{signalName}_{dailyTradeCounter}";

                try
                {
                    // Cancel any existing orders
                    CancelAllOrders();

                    // Place entry order
                    if (direction == MarketPosition.Long)
                    {
                        entryOrder = strategy.EnterLongStopMarket(quantity, entryPrice, uniqueSignal);
                        stopOrder = strategy.ExitLongStopMarket(0, true, quantity, stopPrice, "Stop_" + uniqueSignal, uniqueSignal);
                        target1Order = strategy.ExitLongLimit(0, true, quantity, target1, "Target1_" + uniqueSignal, uniqueSignal);
                        if (target2 > 0)
                            target2Order = strategy.ExitLongLimit(0, true, quantity, target2, "Target2_" + uniqueSignal, uniqueSignal);
                    }
                    else if (direction == MarketPosition.Short)
                    {
                        entryOrder = strategy.EnterShortStopMarket(quantity, entryPrice, uniqueSignal);
                        stopOrder = strategy.ExitShortStopMarket(0, true, quantity, stopPrice, "Stop_" + uniqueSignal, uniqueSignal);
                        target1Order = strategy.ExitShortLimit(0, true, quantity, target1, "Target1_" + uniqueSignal, uniqueSignal);
                        if (target2 > 0)
                            target2Order = strategy.ExitShortLimit(0, true, quantity, target2, "Target2_" + uniqueSignal, uniqueSignal);
                    }

                    entrySignalName = uniqueSignal;
                    logManager?.Log($"EnterTrade: Direction={direction}, Entry={entryPrice}, Stop={stopPrice}, Target1={target1}, Target2={target2}, Qty={quantity}, Signal={uniqueSignal}", "Info");
                }
                catch (Exception ex)
                {
                    logManager?.Log($"[Critical] EnterTrade Exception: {ex.Message}", "Critical");
                }
            }

            public void ExitPosition(string reason, string signalNameSuffix)
            {
                try
                {
                    if (strategy.Position.MarketPosition == MarketPosition.Long)
                        strategy.ExitLong("Exit_" + reason, signalNameSuffix);
                    else if (strategy.Position.MarketPosition == MarketPosition.Short)
                        strategy.ExitShort("Exit_" + reason, signalNameSuffix);

                    logManager?.Log($"ExitPosition: Reason={reason}, Signal={signalNameSuffix}", "Info");
                    CancelAllOrders();
                }
                catch (Exception ex)
                {
                    logManager?.Log($"[Critical] ExitPosition Exception: {ex.Message}", "Critical");
                }
            }

            public void ManageStopLoss(bool useStructureTrail, double atrMultiplier, double structureAtrBuffer)
            {
                // Example: Move stop to structure or ATR-based trailing stop
                // Implement trailing logic as needed
                // Log all stop changes
            }

            public void CancelAllOrders()
            {
                try
                {
                    if (entryOrder != null && entryOrder.OrderState == OrderState.Working)
                        strategy.CancelOrder(entryOrder);
                    if (stopOrder != null && stopOrder.OrderState == OrderState.Working)
                        strategy.CancelOrder(stopOrder);
                    if (target1Order != null && target1Order.OrderState == OrderState.Working)
                        strategy.CancelOrder(target1Order);
                    if (target2Order != null && target2Order.OrderState == OrderState.Working)
                        strategy.CancelOrder(target2Order);
                    logManager?.Log("CancelAllOrders: All working orders cancelled.", "Info");
                }
                catch (Exception ex)
                {
                    logManager?.Log($"[Critical] CancelAllOrders Exception: {ex.Message}", "Critical");
                }
            }

            // Add OnOrderUpdate/OnExecutionUpdate hooks as needed for error handling, partial fills, etc.
            // Add more robust state tracking and logging as needed.

            public void ResetDaily()
            {
                // Reset any daily counters or state here
                // Example: dailyTradeCounter = 0;
            }

        }

        #endregion
    }
}
// --- END OF COMPLETE FILE AlBrooksPriceAction.cs ---